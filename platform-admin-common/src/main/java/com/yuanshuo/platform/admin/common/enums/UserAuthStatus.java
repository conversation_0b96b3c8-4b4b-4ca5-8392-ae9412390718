package com.yuanshuo.platform.admin.common.enums;

/**
 * 用户认证状态
 */
public enum UserAuthStatus {

    INACTIVE("inactive", "未激活"),
    UNVERIFIED("unverified", "未实名"),
    VERIFIED("verified", "已实名"),
    CANCELLED("cancelled", "已注销");

    private final String code;
    private final String info;

    UserAuthStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}