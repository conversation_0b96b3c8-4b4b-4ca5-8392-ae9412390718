package com.yuanshuo.platform.admin.common.core.domain.model;


import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class WxLoginBody {
    /**
     * 微信小程序appId
     */
    private String appId;

    /**
     * 微信小程序Code
     */
    @NotBlank(message = "code不能为空")
    private String code;

    /**
     * 微信小程序手机号Code
     */
    @NotBlank(message = "phoneCode不能为空")
    private String phoneCode;
    /**
     * 加密的数据
     */
    @NotBlank(message = "encryptedData不能为空")
    private String encryptedData;
    /**
     * 加密密钥
     */
    @NotBlank(message = "加密信息不能为空")
    private String iv;
}
