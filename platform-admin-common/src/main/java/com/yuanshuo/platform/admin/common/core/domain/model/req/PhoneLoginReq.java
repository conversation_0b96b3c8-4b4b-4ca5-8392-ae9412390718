package com.yuanshuo.platform.admin.common.core.domain.model.req;

import com.yuanshuo.platform.admin.api.enums.Platform;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 手机号登录请求对象
 *
 * <AUTHOR>
 */
@Data
public class PhoneLoginReq {

    // 手机号
    @NotBlank(message = "手机号不能为空")
    private String phone;

    // 验证码
    @NotBlank(message = "验证码不能为空")
    private String code;

    /**
     * 登录设备类型 android/ios/web
     */
    private String osType;

    /**
     * 是否判断白名单
     */
    private Boolean checkWhiteList = false;


    /**
     * 根据osType查询 Platform 枚举
     */
    public Platform getPlatform() {
        return Platform.APP;
    }



}
