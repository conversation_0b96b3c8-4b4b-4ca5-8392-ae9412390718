package com.yuanshuo.platform.admin.common.core.domain.excel;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import com.yuanshuo.platform.admin.common.annotation.Excels;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysDept;
import lombok.Data;

import java.util.Date;

/**
 * 用户数据导出对象
 *
 * <AUTHOR>
 */
@Data
public class SysUserExcel {

    /** 用户ID */
    @Excel(name = "用户编号", type = Excel.Type.EXPORT, cellType = Excel.ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    /** 用户账号 */
    @Excel(name = "用户名称")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 部门对象 */
    @Excels({
            @Excel(name = "部门", targetAttr = "deptName", type = Excel.Type.EXPORT),
    })
    private SysDept dept;

    /** 手机号码 */
    @Excel(name = "手机号码", cellType = Excel.ColumnType.TEXT)
    private String phonenumber;

    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date createTime;


}
