package com.yuanshuo.platform.admin.common.orchestration;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class MicroserviceOrchestrator {

    private final ConcurrentHashMap<String, ServiceNode> serviceRegistry = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, WorkflowDefinition> workflows = new ConcurrentHashMap<>();
    private final LoadBalancer loadBalancer = new LoadBalancer();
    private final CircuitBreaker circuitBreaker = new CircuitBreaker();
    private final ServiceMesh serviceMesh = new ServiceMesh();
    private final AtomicLong orchestrationCounter = new AtomicLong(0);

    public OrchestrationResult executeWorkflow(String workflowId, WorkflowContext context) {
        long orchestrationId = orchestrationCounter.incrementAndGet();
        long startTime = System.nanoTime();
        
        log.info("开始执行工作流编排：workflowId={}, orchestrationId={}", workflowId, orchestrationId);
        
        WorkflowDefinition workflow = workflows.get(workflowId);
        if (workflow == null) {
            throw new WorkflowNotFoundException("Workflow not found: " + workflowId);
        }
        
        WorkflowExecution execution = new WorkflowExecution(orchestrationId, workflow, context);
        
        try {
            executeWorkflowSteps(execution);
            
            long processingTime = System.nanoTime() - startTime;
            
            return OrchestrationResult.builder()
                    .orchestrationId(orchestrationId)
                    .workflowId(workflowId)
                    .status(ExecutionStatus.SUCCESS)
                    .executedSteps(execution.getExecutedSteps())
                    .processingTimeNanos(processingTime)
                    .completedAt(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("工作流执行失败：workflowId={}, orchestrationId={}, error={}", 
                    workflowId, orchestrationId, e.getMessage(), e);
            
            return OrchestrationResult.builder()
                    .orchestrationId(orchestrationId)
                    .workflowId(workflowId)
                    .status(ExecutionStatus.FAILED)
                    .errorMessage(e.getMessage())
                    .completedAt(LocalDateTime.now())
                    .build();
        }
    }

    public void registerService(String serviceId, ServiceNode node) {
        serviceRegistry.put(serviceId, node);
        serviceMesh.addNode(node);
        
        log.info("服务注册成功：serviceId={}, endpoint={}, version={}", 
                serviceId, node.getEndpoint(), node.getVersion());
    }

    public void registerWorkflow(String workflowId, WorkflowDefinition workflow) {
        workflows.put(workflowId, workflow);
        
        log.info("工作流注册成功：workflowId={}, steps={}", workflowId, workflow.getSteps().size());
    }

    private void executeWorkflowSteps(WorkflowExecution execution) {
        for (WorkflowStep step : execution.getWorkflow().getSteps()) {
            executeStep(execution, step);
        }
    }

    private void executeStep(WorkflowExecution execution, WorkflowStep step) {
        log.debug("执行工作流步骤：orchestrationId={}, stepId={}, serviceId={}", 
                execution.getOrchestrationId(), step.getStepId(), step.getServiceId());
        
        ServiceNode targetService = selectService(step.getServiceId());
        if (targetService == null) {
            throw new ServiceUnavailableException("Service not available: " + step.getServiceId());
        }
        
        if (!circuitBreaker.canExecute(step.getServiceId())) {
            throw new CircuitBreakerOpenException("Circuit breaker open for service: " + step.getServiceId());
        }
        
        try {
            StepResult result = invokeService(targetService, step, execution.getContext());
            execution.addExecutedStep(step.getStepId(), result);
            circuitBreaker.recordSuccess(step.getServiceId());
            
        } catch (Exception e) {
            circuitBreaker.recordFailure(step.getServiceId());
            
            if (step.isRetryable()) {
                retryStep(execution, step);
            } else {
                throw e;
            }
        }
    }

    private ServiceNode selectService(String serviceId) {
        java.util.List<ServiceNode> availableNodes = serviceRegistry.values().stream()
                .filter(node -> serviceId.equals(node.getServiceId()) && node.isHealthy())
                .collect(java.util.stream.Collectors.toList());
        
        if (availableNodes.isEmpty()) {
            return null;
        }
        
        return loadBalancer.selectNode(availableNodes);
    }

    private StepResult invokeService(ServiceNode service, WorkflowStep step, WorkflowContext context) {
        long startTime = System.nanoTime();
        
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(ThreadLocalRandom.current().nextInt(10, 100));
                return "Service response from " + service.getServiceId();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        });
        
        try {
            String response = future.get(step.getTimeoutMs(), java.util.concurrent.TimeUnit.MILLISECONDS);
            long duration = System.nanoTime() - startTime;
            
            return StepResult.builder()
                    .stepId(step.getStepId())
                    .serviceId(service.getServiceId())
                    .response(response)
                    .executionTimeNanos(duration)
                    .status(ExecutionStatus.SUCCESS)
                    .executedAt(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            throw new ServiceInvocationException("Service invocation failed: " + e.getMessage(), e);
        }
    }

    private void retryStep(WorkflowExecution execution, WorkflowStep step) {
        int maxRetries = step.getMaxRetries();
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                Thread.sleep(step.getRetryDelayMs() * attempt);
                
                ServiceNode targetService = selectService(step.getServiceId());
                if (targetService != null) {
                    StepResult result = invokeService(targetService, step, execution.getContext());
                    execution.addExecutedStep(step.getStepId(), result);
                    return;
                }
            } catch (Exception e) {
                log.warn("步骤重试失败：stepId={}, attempt={}/{}, error={}", 
                        step.getStepId(), attempt, maxRetries, e.getMessage());
                
                if (attempt == maxRetries) {
                    throw new MaxRetriesExceededException("Max retries exceeded for step: " + step.getStepId());
                }
            }
        }
    }

    public static class LoadBalancer {
        private final AtomicLong counter = new AtomicLong(0);

        public ServiceNode selectNode(java.util.List<ServiceNode> nodes) {
            if (nodes.isEmpty()) {
                return null;
            }
            
            return nodes.stream()
                    .min((a, b) -> Double.compare(a.getCurrentLoad(), b.getCurrentLoad()))
                    .orElse(nodes.get((int) (counter.incrementAndGet() % nodes.size())));
        }
    }

    public static class CircuitBreaker {
        private final ConcurrentHashMap<String, CircuitState> circuits = new ConcurrentHashMap<>();

        public boolean canExecute(String serviceId) {
            CircuitState state = circuits.computeIfAbsent(serviceId, k -> new CircuitState());
            return state.canExecute();
        }

        public void recordSuccess(String serviceId) {
            CircuitState state = circuits.get(serviceId);
            if (state != null) {
                state.recordSuccess();
            }
        }

        public void recordFailure(String serviceId) {
            CircuitState state = circuits.get(serviceId);
            if (state != null) {
                state.recordFailure();
            }
        }
    }

    public static class CircuitState {
        private int failureCount = 0;
        private int successCount = 0;
        private LocalDateTime lastFailure = null;
        private boolean isOpen = false;

        public boolean canExecute() {
            if (!isOpen) {
                return true;
            }
            
            if (lastFailure != null && lastFailure.plusSeconds(30).isBefore(LocalDateTime.now())) {
                isOpen = false;
                failureCount = 0;
                return true;
            }
            
            return false;
        }

        public void recordSuccess() {
            successCount++;
            if (successCount >= 5) {
                isOpen = false;
                failureCount = 0;
            }
        }

        public void recordFailure() {
            failureCount++;
            lastFailure = LocalDateTime.now();
            
            if (failureCount >= 5) {
                isOpen = true;
            }
        }
    }

    public static class ServiceMesh {
        private final ConcurrentHashMap<String, ServiceNode> meshNodes = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, java.util.List<TrafficRule>> trafficRules = new ConcurrentHashMap<>();

        public void addNode(ServiceNode node) {
            meshNodes.put(node.getNodeId(), node);
            
            TrafficRule defaultRule = new TrafficRule();
            defaultRule.setServiceId(node.getServiceId());
            defaultRule.setWeight(100);
            defaultRule.setEnabled(true);
            
            trafficRules.computeIfAbsent(node.getServiceId(), k -> new java.util.ArrayList<>()).add(defaultRule);
        }

        public java.util.List<ServiceNode> getAvailableNodes(String serviceId) {
            return meshNodes.values().stream()
                    .filter(node -> serviceId.equals(node.getServiceId()) && node.isHealthy())
                    .collect(java.util.stream.Collectors.toList());
        }
    }

    @lombok.Data
    public static class ServiceNode {
        private String nodeId;
        private String serviceId;
        private String endpoint;
        private String version;
        private boolean healthy = true;
        private double currentLoad = 0.0;
        private LocalDateTime lastHealthCheck = LocalDateTime.now();

        public ServiceNode(String nodeId, String serviceId, String endpoint) {
            this.nodeId = nodeId;
            this.serviceId = serviceId;
            this.endpoint = endpoint;
            this.currentLoad = ThreadLocalRandom.current().nextDouble(0.1, 0.8);
        }
    }

    @lombok.Data
    public static class WorkflowDefinition {
        private String workflowId;
        private String name;
        private String version;
        private java.util.List<WorkflowStep> steps = new java.util.ArrayList<>();
        private LocalDateTime createdAt = LocalDateTime.now();
    }

    @lombok.Data
    public static class WorkflowStep {
        private String stepId;
        private String serviceId;
        private String operation;
        private java.util.Map<String, Object> parameters = new ConcurrentHashMap<>();
        private long timeoutMs = 5000;
        private boolean retryable = true;
        private int maxRetries = 3;
        private long retryDelayMs = 1000;
    }

    @lombok.Data
    public static class WorkflowExecution {
        private long orchestrationId;
        private WorkflowDefinition workflow;
        private WorkflowContext context;
        private java.util.Map<String, StepResult> executedSteps = new ConcurrentHashMap<>();
        private LocalDateTime startedAt = LocalDateTime.now();

        public WorkflowExecution(long orchestrationId, WorkflowDefinition workflow, WorkflowContext context) {
            this.orchestrationId = orchestrationId;
            this.workflow = workflow;
            this.context = context;
        }

        public void addExecutedStep(String stepId, StepResult result) {
            executedSteps.put(stepId, result);
        }
    }

    @lombok.Data
    public static class WorkflowContext {
        private String userId;
        private String sessionId;
        private java.util.Map<String, Object> variables = new ConcurrentHashMap<>();
        private LocalDateTime createdAt = LocalDateTime.now();
    }

    @lombok.Data
    public static class TrafficRule {
        private String serviceId;
        private int weight;
        private boolean enabled;
        private java.util.Map<String, String> headers = new ConcurrentHashMap<>();
    }

    @lombok.Builder
    @lombok.Data
    public static class StepResult {
        private String stepId;
        private String serviceId;
        private String response;
        private long executionTimeNanos;
        private ExecutionStatus status;
        private LocalDateTime executedAt;
    }

    @lombok.Builder
    @lombok.Data
    public static class OrchestrationResult {
        private long orchestrationId;
        private String workflowId;
        private ExecutionStatus status;
        private java.util.Map<String, StepResult> executedSteps;
        private String errorMessage;
        private long processingTimeNanos;
        private LocalDateTime completedAt;
    }

    public enum ExecutionStatus {
        SUCCESS, FAILED, TIMEOUT, CANCELLED
    }

    public static class WorkflowNotFoundException extends RuntimeException {
        public WorkflowNotFoundException(String message) {
            super(message);
        }
    }

    public static class ServiceUnavailableException extends RuntimeException {
        public ServiceUnavailableException(String message) {
            super(message);
        }
    }

    public static class CircuitBreakerOpenException extends RuntimeException {
        public CircuitBreakerOpenException(String message) {
            super(message);
        }
    }

    public static class ServiceInvocationException extends RuntimeException {
        public ServiceInvocationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public static class MaxRetriesExceededException extends RuntimeException {
        public MaxRetriesExceededException(String message) {
            super(message);
        }
    }
}