package com.yuanshuo.platform.admin.common.qywx.service;

import com.yuanshuo.platform.admin.common.qywx.config.WxCpConfiguration;
import com.yuanshuo.platform.admin.common.qywx.config.WxCpProperties;
import me.chanjar.weixin.cp.api.WxCpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 企业微信服务助手
 *
 * <AUTHOR>
 */
@Service
public class WxCpHelper {

    @Autowired
    private WxCpProperties properties;

    @Autowired
    private WxCpConfiguration wxCpConfiguration;

    /**
     * 获取企业微信服务
     */
    public WxCpService getCpService(Integer agentId) {
        return WxCpConfiguration.getCpService(agentId);
    }

    /**
     * 获取企业微信服务-销售小工具
     */
    public WxCpService getCpService() {
        return WxCpConfiguration.getCpService(properties.getAppConfigs().get(0).getAgentId());
    }

    /**
     * 获取企业微信登录地址
     *
     * @return
     */
    public String getQywxWebLoginUrl(String env) {
        // 企业微信配置的回调域名
        String callbackDomain = properties.getDomain();
        return wxCpConfiguration.getQywxWebLoginUrl(callbackDomain, env);
    }

    /**
     * 获取回调地址
     *
     * @param env 环境
     * @return
     */
    public String getCallbackUrl(String env) {
        return wxCpConfiguration.getCallbackUrl(env);
    }

}
