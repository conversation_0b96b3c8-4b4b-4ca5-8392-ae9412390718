package com.yuanshuo.platform.admin.common.core.domain.excel;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 *  C端用户数据导出对象
 *
 * <AUTHOR>
 */
@Data
public class CUserExcel {

    /** 手机号码 */
    @Excel(name = "手机号码", cellType = Excel.ColumnType.TEXT)
    private String phonenumber;

    /** 用户账号 */
    @Excel(name = "微信openid")
    private String userName;

    /** 用户ID */
    @Excel(name = "用户编号", type = Excel.Type.EXPORT, cellType = Excel.ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date createTime;

}
