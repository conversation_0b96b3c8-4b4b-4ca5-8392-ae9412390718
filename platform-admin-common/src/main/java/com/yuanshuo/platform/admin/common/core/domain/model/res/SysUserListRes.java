package com.yuanshuo.platform.admin.common.core.domain.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysDept;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息返回对象
 *
 * <AUTHOR>
 */
@Data
public class SysUserListRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 用户编号 */
    private Long userId;

    /** 微信openId */
    private String openId;

    /** 用户名称 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 部门 */
    private SysDept dept;

    /** 手机号 */
    private String phonenumber;

    /** 状态 */
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
