package com.yuanshuo.platform.admin.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

@Slf4j
public class UserQueryPerformanceMonitor {

    private final LongAdder totalQueryCount = new LongAdder();
    private final LongAdder slowQueryCount = new LongAdder();
    private final LongAdder totalQueryTime = new LongAdder();
    private final AtomicLong maxQueryTime = new AtomicLong(0);
    private final AtomicLong minQueryTime = new AtomicLong(Long.MAX_VALUE);
    private final ConcurrentHashMap<String, QueryTypeMetrics> queryTypeMetrics = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, HourlyMetrics> hourlyMetrics = new ConcurrentHashMap<>();
    public void recordQuery(String queryType, long duration, int resultCount) {
        totalQueryCount.increment();
        totalQueryTime.add(duration);
        
        updateMaxTime(duration);
        updateMinTime(duration);
        
        if (duration > 1000) {
            slowQueryCount.increment();
            log.warn("检测到慢查询：类型={}, 耗时={}ms, 结果数={}", queryType, duration, resultCount);
        }
        
        queryTypeMetrics.computeIfAbsent(queryType, k -> new QueryTypeMetrics())
                .recordQuery(duration, resultCount);
        
        int currentHour = LocalDateTime.now().getHour();
        hourlyMetrics.computeIfAbsent(currentHour, k -> new HourlyMetrics())
                .recordQuery(duration);
        
        analyzePerformance(queryType, duration, resultCount);
    }
    public PerformanceReport getPerformanceReport() {
        long totalQueries = totalQueryCount.sum();
        long totalTime = totalQueryTime.sum();
        
        return PerformanceReport.builder()
                .totalQueries(totalQueries)
                .slowQueries(slowQueryCount.sum())
                .averageTime(totalQueries > 0 ? (double) totalTime / totalQueries : 0)
                .maxTime(maxQueryTime.get())
                .minTime(minQueryTime.get() == Long.MAX_VALUE ? 0 : minQueryTime.get())
                .slowQueryRate(totalQueries > 0 ? (double) slowQueryCount.sum() / totalQueries * 100 : 0)
                .queryTypeMetrics(new ConcurrentHashMap<>(queryTypeMetrics))
                .hourlyMetrics(new ConcurrentHashMap<>(hourlyMetrics))
                .recommendations(generateRecommendations())
                .build();
    }

    public void reset() {
        totalQueryCount.reset();
        slowQueryCount.reset();
        totalQueryTime.reset();
        maxQueryTime.set(0);
        minQueryTime.set(Long.MAX_VALUE);
        queryTypeMetrics.clear();
        hourlyMetrics.clear();
        
        log.info("性能监控统计数据已重置");
    }

    private void updateMaxTime(long duration) {
        long current = maxQueryTime.get();
        while (duration > current && !maxQueryTime.compareAndSet(current, duration)) {
            current = maxQueryTime.get();
        }
    }

    private void updateMinTime(long duration) {
        long current = minQueryTime.get();
        while (duration < current && !minQueryTime.compareAndSet(current, duration)) {
            current = minQueryTime.get();
        }
    }

    private void analyzePerformance(String queryType, long duration, int resultCount) {
        if (duration > 2000) {
            log.warn("超慢查询告警：类型={}, 耗时={}ms, 建议优化索引或查询条件", queryType, duration);
        }
        
        if (resultCount > 10000) {
            log.warn("大结果集告警：类型={}, 结果数={}, 建议增加分页限制", queryType, resultCount);
        }
        
        long usedMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        long maxMemory = Runtime.getRuntime().maxMemory();
        double memoryUsage = (double) usedMemory / maxMemory * 100;
        
        if (memoryUsage > 80) {
            log.warn("内存使用率过高：{}%, 建议优化查询或增加内存", String.format("%.2f", memoryUsage));
        }
    }

    private java.util.List<String> generateRecommendations() {
        java.util.List<String> recommendations = new java.util.ArrayList<>();
        
        long totalQueries = totalQueryCount.sum();
        if (totalQueries == 0) {
            return recommendations;
        }
        
        double slowQueryRate = (double) slowQueryCount.sum() / totalQueries * 100;
        if (slowQueryRate > 5) {
            recommendations.add("慢查询比例过高(" + String.format("%.2f", slowQueryRate) + "%)，建议优化数据库索引");
        }
        
        long avgTime = totalQueryTime.sum() / totalQueries;
        if (avgTime > 500) {
            recommendations.add("平均查询时间过长(" + avgTime + "ms)，建议优化查询条件或增加缓存");
        }
        
        if (maxQueryTime.get() > 5000) {
            recommendations.add("存在超长查询(" + maxQueryTime.get() + "ms)，建议检查查询逻辑");
        }
        
        return recommendations;
    }

    public static class QueryTypeMetrics {
        private final LongAdder count = new LongAdder();
        private final LongAdder totalTime = new LongAdder();
        private final LongAdder totalResults = new LongAdder();
        
        public void recordQuery(long duration, int resultCount) {
            count.increment();
            totalTime.add(duration);
            totalResults.add(resultCount);
        }
        
        public long getCount() { return count.sum(); }
        public long getTotalTime() { return totalTime.sum(); }
        public double getAverageTime() { 
            long c = count.sum();
            return c > 0 ? (double) totalTime.sum() / c : 0; 
        }
        public double getAverageResults() { 
            long c = count.sum();
            return c > 0 ? (double) totalResults.sum() / c : 0; 
        }
    }

    public static class HourlyMetrics {
        private final LongAdder count = new LongAdder();
        private final LongAdder totalTime = new LongAdder();
        
        public void recordQuery(long duration) {
            count.increment();
            totalTime.add(duration);
        }
        
        public long getCount() { return count.sum(); }
        public double getAverageTime() { 
            long c = count.sum();
            return c > 0 ? (double) totalTime.sum() / c : 0; 
        }
    }

    /**
     * 性能报告
     */
    @lombok.Builder
    @lombok.Data
    public static class PerformanceReport {
        private long totalQueries;
        private long slowQueries;
        private double averageTime;
        private long maxTime;
        private long minTime;
        private double slowQueryRate;
        private ConcurrentHashMap<String, QueryTypeMetrics> queryTypeMetrics;
        private ConcurrentHashMap<Integer, HourlyMetrics> hourlyMetrics;
        private java.util.List<String> recommendations;
    }
}