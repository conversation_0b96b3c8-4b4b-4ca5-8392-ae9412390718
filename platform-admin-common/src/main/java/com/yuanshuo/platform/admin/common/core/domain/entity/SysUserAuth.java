package com.yuanshuo.platform.admin.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuanshuo.platform.admin.common.core.domain.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户登录方式表
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_user_auth")
public class SysUserAuth {

    /** 主键 */
    private Long id;

    /** 用户id */
    private Long userId;

    /** 登录类型（Phone/Email/WxMp/AliMp/WeCom/Password） */
    private String authType;

    /** 登录标识（手机号/邮箱/第三方openid） */
    private String identifier;

    /** 状态 1开通 -1禁用 */
    private Integer status;

    /***创建时间*/
    private LocalDateTime createTime;

    /***更新时间*/
    private LocalDateTime updateTime;

    @TableLogic
    /** 删除标识 0未删除 1已删除 */
    private Integer isDelete;

}
