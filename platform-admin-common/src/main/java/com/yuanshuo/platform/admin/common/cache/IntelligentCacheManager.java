package com.yuanshuo.platform.admin.common.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class IntelligentCacheManager {

    private final ConcurrentHashMap<String, CacheMetrics> cacheMetrics = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> hotDataCounter = new ConcurrentHashMap<>();
    private final ScheduledExecutorService preloadScheduler = Executors.newScheduledThreadPool(2);
    private final ScheduledExecutorService cleanupScheduler = Executors.newScheduledThreadPool(1);

    public IntelligentCacheManager() {
        startScheduledTasks();
    }
    public void recordCacheHit(String cacheKey, String cacheRegion) {
        CacheMetrics metrics = cacheMetrics.computeIfAbsent(cacheRegion, k -> new CacheMetrics());
        metrics.recordHit();
        
        hotDataCounter.computeIfAbsent(cacheKey, k -> new AtomicLong(0)).incrementAndGet();
        
        log.debug("缓存命中：region={}, key={}, 命中率={:.2f}%", 
                cacheRegion, cacheKey, metrics.getHitRate() * 100);
    }
    public void recordCacheMiss(String cacheKey, String cacheRegion) {
        CacheMetrics metrics = cacheMetrics.computeIfAbsent(cacheRegion, k -> new CacheMetrics());
        metrics.recordMiss();
        
        log.debug("缓存未命中：region={}, key={}, 命中率={:.2f}%", 
                cacheRegion, cacheKey, metrics.getHitRate() * 100);
        
        if (shouldPreload(cacheKey, cacheRegion)) {
            schedulePreload(cacheKey, cacheRegion);
        }
    }
    public CacheReport getCacheReport() {
        CacheReport report = new CacheReport();
        report.setGeneratedAt(LocalDateTime.now());
        
        cacheMetrics.forEach((region, metrics) -> {
            CacheRegionStats stats = new CacheRegionStats();
            stats.setRegion(region);
            stats.setHitCount(metrics.getHitCount());
            stats.setMissCount(metrics.getMissCount());
            stats.setHitRate(metrics.getHitRate());
            stats.setTotalRequests(metrics.getTotalRequests());
            
            report.getRegionStats().put(region, stats);
        });
        
        report.setHotDataKeys(identifyHotData());
        report.setRecommendations(generateCacheRecommendations());
        
        return report;
    }

    /**
     * 智能缓存预热
     */
    public void intelligentPreload(String cacheRegion) {
        preloadScheduler.execute(() -> {
            try {
                log.info("开始智能缓存预热：region={}", cacheRegion);
                
                // 模拟预热逻辑
                Thread.sleep(100);
                
                // 预热热点数据
                java.util.List<String> hotKeys = identifyHotData();
                for (String key : hotKeys.subList(0, Math.min(10, hotKeys.size()))) {
                    // 这里应该调用实际的数据加载逻辑
                    log.debug("预热缓存数据：key={}", key);
                }
                
                log.info("智能缓存预热完成：region={}, 预热数据量={}", cacheRegion, Math.min(10, hotKeys.size()));
                
            } catch (Exception e) {
                log.error("缓存预热失败：region={}, 错误={}", cacheRegion, e.getMessage(), e);
            }
        });
    }

    /**
     * 自适应缓存清理
     */
    public void adaptiveCacheCleanup() {
        cleanupScheduler.execute(() -> {
            try {
                log.info("开始自适应缓存清理");
                
                // 内存使用率检查
                Runtime runtime = Runtime.getRuntime();
                long usedMemory = runtime.totalMemory() - runtime.freeMemory();
                long maxMemory = runtime.maxMemory();
                double memoryUsage = (double) usedMemory / maxMemory;
                
                if (memoryUsage > 0.8) {
                    log.warn("内存使用率过高：{:.2f}%，开始清理低频缓存", memoryUsage * 100);
                    cleanupLowFrequencyCache();
                }
                
                // 清理过期统计数据
                cleanupExpiredMetrics();
                
                log.info("自适应缓存清理完成");
                
            } catch (Exception e) {
                log.error("缓存清理失败：{}", e.getMessage(), e);
            }
        });
    }

    private void startScheduledTasks() {
        // 每5分钟执行一次缓存预热
        preloadScheduler.scheduleAtFixedRate(() -> {
            cacheMetrics.keySet().forEach(this::intelligentPreload);
        }, 5, 5, TimeUnit.MINUTES);
        
        // 每10分钟执行一次缓存清理
        cleanupScheduler.scheduleAtFixedRate(this::adaptiveCacheCleanup, 10, 10, TimeUnit.MINUTES);
        
        // 每小时输出缓存统计报告
        cleanupScheduler.scheduleAtFixedRate(() -> {
            CacheReport report = getCacheReport();
            log.info("缓存统计报告：{}", report);
        }, 1, 1, TimeUnit.HOURS);
    }

    private boolean shouldPreload(String cacheKey, String cacheRegion) {
        CacheMetrics metrics = cacheMetrics.get(cacheRegion);
        if (metrics == null) return false;
        
        // 如果命中率低于50%且请求量大于100，则考虑预热
        return metrics.getHitRate() < 0.5 && metrics.getTotalRequests() > 100;
    }

    private void schedulePreload(String cacheKey, String cacheRegion) {
        preloadScheduler.schedule(() -> {
            log.info("执行缓存预热：region={}, key={}", cacheRegion, cacheKey);
            // 这里应该调用实际的数据加载逻辑
        }, 1, TimeUnit.SECONDS);
    }

    private java.util.List<String> identifyHotData() {
        return hotDataCounter.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue().get(), e1.getValue().get()))
                .limit(20)
                .map(java.util.Map.Entry::getKey)
                .collect(java.util.stream.Collectors.toList());
    }

    private java.util.List<String> generateCacheRecommendations() {
        java.util.List<String> recommendations = new java.util.ArrayList<>();
        
        cacheMetrics.forEach((region, metrics) -> {
            double hitRate = metrics.getHitRate();
            long totalRequests = metrics.getTotalRequests();
            
            if (hitRate < 0.3 && totalRequests > 1000) {
                recommendations.add("缓存区域 " + region + " 命中率过低(" + String.format("%.2f", hitRate * 100) + "%)，建议优化缓存策略");
            }
            
            if (hitRate > 0.95 && totalRequests > 5000) {
                recommendations.add("缓存区域 " + region + " 表现优秀，可考虑增加缓存容量");
            }
        });
        
        return recommendations;
    }

    private void cleanupLowFrequencyCache() {
        // 清理访问频率低的缓存数据
        hotDataCounter.entrySet().removeIf(entry -> entry.getValue().get() < 5);
        log.info("已清理低频缓存数据");
    }

    private void cleanupExpiredMetrics() {
        // 重置统计数据（模拟清理过期数据）
        cacheMetrics.values().forEach(CacheMetrics::reset);
        log.debug("已清理过期统计数据");
    }

    /**
     * 缓存指标
     */
    public static class CacheMetrics {
        private final AtomicLong hitCount = new AtomicLong(0);
        private final AtomicLong missCount = new AtomicLong(0);
        
        public void recordHit() { hitCount.incrementAndGet(); }
        public void recordMiss() { missCount.incrementAndGet(); }
        
        public long getHitCount() { return hitCount.get(); }
        public long getMissCount() { return missCount.get(); }
        public long getTotalRequests() { return hitCount.get() + missCount.get(); }
        
        public double getHitRate() {
            long total = getTotalRequests();
            return total > 0 ? (double) hitCount.get() / total : 0.0;
        }
        
        public void reset() {
            hitCount.set(0);
            missCount.set(0);
        }
    }

    /**
     * 缓存区域统计
     */
    @lombok.Data
    public static class CacheRegionStats {
        private String region;
        private long hitCount;
        private long missCount;
        private long totalRequests;
        private double hitRate;
    }

    /**
     * 缓存报告
     */
    @lombok.Data
    public static class CacheReport {
        private LocalDateTime generatedAt;
        private java.util.Map<String, CacheRegionStats> regionStats = new ConcurrentHashMap<>();
        private java.util.List<String> hotDataKeys = new java.util.ArrayList<>();
        private java.util.List<String> recommendations = new java.util.ArrayList<>();
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("缓存统计报告 [").append(generatedAt).append("]\n");
            regionStats.forEach((region, stats) -> {
                sb.append("  区域: ").append(region)
                  .append(", 命中率: ").append(String.format("%.2f%%", stats.hitRate * 100))
                  .append(", 总请求: ").append(stats.totalRequests).append("\n");
            });
            return sb.toString();
        }
    }
}