package com.yuanshuo.platform.admin.common.blockchain;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
public class BlockchainValidator {

    private final ConcurrentHashMap<String, Block> blockchain = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, TransactionPool> transactionPools = new ConcurrentHashMap<>();
    private final ConsensusEngine consensusEngine = new ConsensusEngine();
    private final CryptographicHasher hasher = new CryptographicHasher();
    private String lastBlockHash = "genesis";

    public ValidationResult validateDataIntegrity(String dataId, Object data) {
        long startTime = System.nanoTime();
        
        Transaction transaction = createTransaction(dataId, data);
        Block block = mineBlock(transaction);
        
        boolean isValid = validateBlock(block);
        if (isValid) {
            addBlockToChain(block);
        }
        
        long processingTime = System.nanoTime() - startTime;
        
        return ValidationResult.builder()
                .dataId(dataId)
                .blockHash(block.getHash())
                .isValid(isValid)
                .consensusScore(consensusEngine.calculateConsensusScore(block))
                .processingTimeNanos(processingTime)
                .validatedAt(LocalDateTime.now())
                .build();
    }

    public MerkleProof generateMerkleProof(String dataId) {
        Block block = findBlockContainingData(dataId);
        if (block == null) {
            return null;
        }
        
        MerkleTree merkleTree = new MerkleTree(block.getTransactions());
        return merkleTree.generateProof(dataId);
    }

    public boolean verifyMerkleProof(MerkleProof proof, String rootHash) {
        return MerkleTree.verifyProof(proof, rootHash);
    }

    private Transaction createTransaction(String dataId, Object data) {
        Transaction transaction = new Transaction();
        transaction.setId(generateTransactionId());
        transaction.setDataId(dataId);
        transaction.setDataHash(hasher.hash(data.toString()));
        transaction.setTimestamp(LocalDateTime.now());
        transaction.setNonce(ThreadLocalRandom.current().nextLong());
        
        return transaction;
    }

    private Block mineBlock(Transaction transaction) {
        Block block = new Block();
        block.setIndex(blockchain.size());
        block.setPreviousHash(lastBlockHash);
        block.addTransaction(transaction);
        block.setTimestamp(LocalDateTime.now());
        
        ProofOfWork pow = new ProofOfWork(block);
        pow.mine();
        
        return block;
    }

    private boolean validateBlock(Block block) {
        if (!hasher.hash(block.getPreviousHash() + block.getData()).startsWith("0000")) {
            return false;
        }
        
        if (!consensusEngine.validateConsensus(block)) {
            return false;
        }
        
        return validateTransactions(block.getTransactions());
    }

    private boolean validateTransactions(java.util.List<Transaction> transactions) {
        return transactions.stream().allMatch(this::validateTransaction);
    }

    private boolean validateTransaction(Transaction transaction) {
        return transaction.getId() != null && 
               transaction.getDataHash() != null && 
               transaction.getTimestamp() != null;
    }

    private void addBlockToChain(Block block) {
        String blockHash = hasher.hash(block.toString());
        block.setHash(blockHash);
        blockchain.put(blockHash, block);
        lastBlockHash = blockHash;
        
        log.info("区块添加到链：hash={}, index={}, transactions={}", 
                blockHash, block.getIndex(), block.getTransactions().size());
    }

    private Block findBlockContainingData(String dataId) {
        return blockchain.values().stream()
                .filter(block -> block.getTransactions().stream()
                        .anyMatch(tx -> dataId.equals(tx.getDataId())))
                .findFirst()
                .orElse(null);
    }

    private String generateTransactionId() {
        return "tx_" + System.currentTimeMillis() + "_" + ThreadLocalRandom.current().nextInt(10000);
    }

    public static class ConsensusEngine {
        private final java.util.List<Node> nodes = java.util.Arrays.asList(
                new Node("node1", 0.8),
                new Node("node2", 0.9),
                new Node("node3", 0.7)
        );

        public double calculateConsensusScore(Block block) {
            return nodes.stream()
                    .mapToDouble(node -> node.validateBlock(block))
                    .average()
                    .orElse(0.0);
        }

        public boolean validateConsensus(Block block) {
            long approvals = nodes.stream()
                    .mapToLong(node -> node.validateBlock(block) > 0.6 ? 1 : 0)
                    .sum();
            
            return approvals >= (nodes.size() * 2 / 3);
        }
    }

    public static class Node {
        private String id;
        private double reliability;

        public Node(String id, double reliability) {
            this.id = id;
            this.reliability = reliability;
        }

        public double validateBlock(Block block) {
            double baseScore = ThreadLocalRandom.current().nextDouble(0.5, 1.0);
            return baseScore * reliability;
        }
    }

    public static class CryptographicHasher {
        public String hash(String input) {
            try {
                MessageDigest digest = MessageDigest.getInstance("SHA-256");
                byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
                StringBuilder hexString = new StringBuilder();
                
                for (byte b : hash) {
                    String hex = Integer.toHexString(0xff & b);
                    if (hex.length() == 1) {
                        hexString.append('0');
                    }
                    hexString.append(hex);
                }
                
                return hexString.toString();
            } catch (Exception e) {
                return "hash_error_" + System.currentTimeMillis();
            }
        }
    }

    public static class ProofOfWork {
        private Block block;
        private int difficulty = 4;

        public ProofOfWork(Block block) {
            this.block = block;
        }

        public void mine() {
            String target = "1";
            long nonce = 0;
            String hash;
            
            do {
                nonce++;
                hash = calculateHash(nonce);
            } while (!hash.startsWith(target) && nonce < 1000000);
            
            block.setNonce(nonce);
            block.setHash(hash);
        }

        private String calculateHash(long nonce) {
            CryptographicHasher hasher = new CryptographicHasher();
            return hasher.hash(block.getPreviousHash() + block.getData() + nonce);
        }
    }

    public static class MerkleTree {
        private java.util.List<Transaction> transactions;
        private String rootHash;

        public MerkleTree(java.util.List<Transaction> transactions) {
            this.transactions = transactions;
            this.rootHash = buildTree();
        }

        private String buildTree() {
            if (transactions.isEmpty()) {
                return "empty_tree";
            }
            
            java.util.List<String> hashes = transactions.stream()
                    .map(tx -> new CryptographicHasher().hash(tx.toString()))
                    .collect(java.util.stream.Collectors.toList());
            
            while (hashes.size() > 1) {
                java.util.List<String> newLevel = new java.util.ArrayList<>();
                
                for (int i = 0; i < hashes.size(); i += 2) {
                    String left = hashes.get(i);
                    String right = i + 1 < hashes.size() ? hashes.get(i + 1) : left;
                    String combined = new CryptographicHasher().hash(left + right);
                    newLevel.add(combined);
                }
                
                hashes = newLevel;
            }
            
            return hashes.get(0);
        }

        public MerkleProof generateProof(String dataId) {
            Transaction target = transactions.stream()
                    .filter(tx -> dataId.equals(tx.getDataId()))
                    .findFirst()
                    .orElse(null);
            
            if (target == null) {
                return null;
            }
            
            java.util.List<String> proof = new java.util.ArrayList<>();
            proof.add(new CryptographicHasher().hash(target.toString()));
            
            return new MerkleProof(dataId, proof, rootHash);
        }

        public static boolean verifyProof(MerkleProof proof, String rootHash) {
            return rootHash.equals(proof.getRootHash());
        }
    }

    @lombok.Data
    public static class Block {
        private int index;
        private String previousHash;
        private java.util.List<Transaction> transactions = new java.util.ArrayList<>();
        private LocalDateTime timestamp;
        private long nonce;
        private String hash;

        public void addTransaction(Transaction transaction) {
            transactions.add(transaction);
        }

        public String getData() {
            return transactions.stream()
                    .map(Transaction::toString)
                    .collect(java.util.stream.Collectors.joining(","));
        }
    }

    @lombok.Data
    public static class Transaction {
        private String id;
        private String dataId;
        private String dataHash;
        private LocalDateTime timestamp;
        private long nonce;
    }

    @lombok.Data
    public static class TransactionPool {
        private String poolId;
        private java.util.List<Transaction> pendingTransactions = new java.util.ArrayList<>();
        private int maxSize = 1000;

        public boolean addTransaction(Transaction transaction) {
            if (pendingTransactions.size() >= maxSize) {
                return false;
            }
            pendingTransactions.add(transaction);
            return true;
        }
    }

    @lombok.Data
    @lombok.AllArgsConstructor
    public static class MerkleProof {
        private String dataId;
        private java.util.List<String> proof;
        private String rootHash;
    }

    @lombok.Builder
    @lombok.Data
    public static class ValidationResult {
        private String dataId;
        private String blockHash;
        private boolean isValid;
        private double consensusScore;
        private long processingTimeNanos;
        private LocalDateTime validatedAt;
    }
}