package com.yuanshuo.platform.admin.common.qywx.config;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.yuanshuo.platform.admin.common.exception.ServerErrorCode;
import com.yuanshuo.platform.admin.common.qywx.handler.MsgHandler;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单实例配置
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(WxCpProperties.class)
public class WxCpConfiguration {
    private MsgHandler msgHandler;

    private WxCpProperties properties;

    private static Map<Integer, WxCpMessageRouter> routers = Maps.newHashMap();
    private static Map<Integer, WxCpService> cpServices = Maps.newHashMap();

    // 企业微信web登录地址
    private String qywxWebLoginUrl = "https://login.work.weixin.qq.com/wwlogin/sso/login?login_type=%s&appid=%s&agentid=%s&redirect_uri=%s";


    @Autowired
    public WxCpConfiguration(MsgHandler msgHandler,
                             WxCpProperties properties) {
        this.msgHandler = msgHandler;
        this.properties = properties;
    }


    public static Map<Integer, WxCpMessageRouter> getRouters() {
        return routers;
    }

    public static WxCpService getCpService(Integer agentId) {
        return cpServices.get(agentId);
    }

    @PostConstruct
    public void initServices() {
        cpServices = this.properties.getAppConfigs().stream().map(a -> {
            val configStorage = new WxCpDefaultConfigImpl();
            configStorage.setCorpId(this.properties.getCorpId());
            configStorage.setAgentId(a.getAgentId());
            configStorage.setCorpSecret(a.getSecret());
            configStorage.setToken(a.getToken());
            configStorage.setAesKey(a.getAesKey());
            val service = new WxCpServiceImpl();
            service.setWxCpConfigStorage(configStorage);
            routers.put(a.getAgentId(), this.newRouter(service));
            return service;
        }).collect(Collectors.toMap(service -> service.getWxCpConfigStorage().getAgentId(), a -> a));

    }

    private WxCpMessageRouter newRouter(WxCpService wxCpService) {
        final val newRouter = new WxCpMessageRouter(wxCpService);

        // 默认
        newRouter.rule().async(false).handler(this.msgHandler).end();

        return newRouter;
    }

    /**
     * 企业微信web登录地址
     */
    public String getQywxWebLoginUrl(String callbackDomain, String state) {
        checkEnv(state);
        // 构建 redirectUrl 参数值
        String redirectUrl = String.format("https://%s/prod-api/user/qywxLoginCallback?state=%s", callbackDomain, state);
//        if (StrUtil.equals("prod", state)) {
//            redirectUrl = String.format("https://%s/prod-api/user/qywxLoginCallback?state=%s", callbackDomain, state);
//        }

        String encodedRedirectUrl;
        try {
            encodedRedirectUrl = URLEncoder.encode(redirectUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("URL encode error", e);
            throw ServerErrorCode.ERROR.exp("URL encoding failed");
        }

        String url = String.format(qywxWebLoginUrl, "CorpApp", this.properties.getCorpId(), this.properties.getAppConfigs().get(0).getAgentId() + "", encodedRedirectUrl);
        return url;
    }

    /**
     * 根据 env 获取对应的回调地址
     */
    public String getCallbackUrl(String env) {
        if (env == null || env.isEmpty()) {
            throw ServerErrorCode.ERROR.exp("env参数不能为空");
        }

        return this.properties.getRedirectUrls().stream()
                .filter(redirectUrl -> env.contains(redirectUrl.getEnv()))
                .findFirst()
                .map(redirectUrl -> redirectUrl.getUrl())
                .orElseThrow(() -> ServerErrorCode.ERROR.exp("env参数错误，不存在对应的回调地址" + env));
    }

    /**
     * 校验环境是否存在
     */
    private void checkEnv(String env) {
        if (env == null || env.isEmpty()) {
            throw ServerErrorCode.ERROR.exp("env参数不能为空");
        }

        boolean b = this.properties.getRedirectUrls().stream()
                .anyMatch(redirectUrl -> StrUtil.equals(redirectUrl.getEnv(), env));
        if (!b) {
            throw ServerErrorCode.ERROR.exp("env参数错误，不存在对应的回调地址" + env);
        }
    }

}
