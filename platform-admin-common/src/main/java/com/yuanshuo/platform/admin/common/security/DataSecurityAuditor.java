package com.yuanshuo.platform.admin.common.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;

@Slf4j
public class DataSecurityAuditor {

    private final ConcurrentLinkedQueue<SensitiveDataAccess> accessLog = new ConcurrentLinkedQueue<>();
    private final ConcurrentHashMap<String, UserOperationStats> userStats = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> anomalyCounter = new ConcurrentHashMap<>();
    private static final Pattern PHONE_PATTERN = Pattern.compile("1[3-9]\\d{9}");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("\\d{17}[\\dXx]");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}");

    public void auditDataAccess(String userId, String operation, String dataType, Object data) {
        try {
            SensitivityLevel level = detectSensitivity(data);
            
            SensitiveDataAccess access = SensitiveDataAccess.builder()
                    .userId(userId)
                    .operation(operation)
                    .dataType(dataType)
                    .sensitivityLevel(level)
                    .accessTime(LocalDateTime.now())
                    .ipAddress(getCurrentUserIP())
                    .userAgent(getCurrentUserAgent())
                    .build();
            
            accessLog.offer(access);
            
            updateUserStats(userId, operation, level);
            
            detectAnomalousActivity(userId, operation, level);
            
            if (level.ordinal() >= SensitivityLevel.MEDIUM.ordinal()) {
                log.info("敏感数据访问审计：用户={}, 操作={}, 数据类型={}, 敏感级别={}", 
                        userId, operation, dataType, level);
            }
            
        } catch (Exception e) {
            log.error("数据安全审计失败：用户={}, 操作={}, 错误={}", userId, operation, e.getMessage(), e);
        }
    }

    /**
     * 数据脱敏处理
     */
    public String maskSensitiveData(String data, DataType dataType) {
        if (data == null || data.trim().isEmpty()) {
            return data;
        }
        
        switch (dataType) {
            case PHONE:
                return maskPhone(data);
            case ID_CARD:
                return maskIdCard(data);
            case EMAIL:
                return maskEmail(data);
            case NAME:
                return maskName(data);
            default:
                return data;
        }
    }

    /**
     * 获取安全审计报告
     */
    public SecurityAuditReport getSecurityReport() {
        SecurityAuditReport report = new SecurityAuditReport();
        report.setGeneratedAt(LocalDateTime.now());
        
        // 统计敏感数据访问
        long totalAccess = accessLog.size();
        long highSensitivityAccess = accessLog.stream()
                .mapToLong(access -> access.getSensitivityLevel() == SensitivityLevel.HIGH ? 1 : 0)
                .sum();
        
        report.setTotalDataAccess(totalAccess);
        report.setHighSensitivityAccess(highSensitivityAccess);
        
        // 用户风险评估
        report.setUserRiskAssessment(assessUserRisks());
        
        // 异常行为统计
        report.setAnomalousActivities(new ConcurrentHashMap<>(anomalyCounter));
        
        // 安全建议
        report.setSecurityRecommendations(generateSecurityRecommendations());
        
        return report;
    }

    /**
     * 清理过期审计数据
     */
    public void cleanupExpiredAuditData() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        
        // 清理30天前的访问记录
        accessLog.removeIf(access -> access.getAccessTime().isBefore(cutoffTime));
        
        // 重置异常计数器
        anomalyCounter.clear();
        
        log.info("已清理过期审计数据，保留最近30天的记录");
    }

    private SensitivityLevel detectSensitivity(Object data) {
        if (data == null) {
            return SensitivityLevel.LOW;
        }
        
        String dataStr = data.toString();
        
        // 检测手机号
        if (PHONE_PATTERN.matcher(dataStr).find()) {
            return SensitivityLevel.HIGH;
        }
        
        // 检测身份证号
        if (ID_CARD_PATTERN.matcher(dataStr).find()) {
            return SensitivityLevel.HIGH;
        }
        
        // 检测邮箱
        if (EMAIL_PATTERN.matcher(dataStr).find()) {
            return SensitivityLevel.MEDIUM;
        }
        
        // 检测可能的姓名（简单判断）
        if (dataStr.length() >= 2 && dataStr.length() <= 10 && 
            dataStr.matches("[\u4e00-\u9fa5a-zA-Z\\s]+")) {
            return SensitivityLevel.MEDIUM;
        }
        
        return SensitivityLevel.LOW;
    }

    private void updateUserStats(String userId, String operation, SensitivityLevel level) {
        UserOperationStats stats = userStats.computeIfAbsent(userId, k -> new UserOperationStats());
        stats.recordOperation(operation, level);
    }

    private void detectAnomalousActivity(String userId, String operation, SensitivityLevel level) {
        UserOperationStats stats = userStats.get(userId);
        if (stats == null) return;
        
        // 检测频繁访问
        if (stats.getOperationCount(operation) > 1000) {
            String anomalyKey = userId + ":" + operation + ":frequent_access";
            anomalyCounter.computeIfAbsent(anomalyKey, k -> new AtomicLong(0)).incrementAndGet();
            log.warn("检测到异常行为：用户 {} 频繁执行操作 {}", userId, operation);
        }
        
        // 检测敏感数据大量访问
        if (level == SensitivityLevel.HIGH && stats.getHighSensitivityCount() > 500) {
            String anomalyKey = userId + ":high_sensitivity_access";
            anomalyCounter.computeIfAbsent(anomalyKey, k -> new AtomicLong(0)).incrementAndGet();
            log.warn("检测到异常行为：用户 {} 大量访问敏感数据", userId);
        }
    }

    private String maskPhone(String phone) {
        if (phone.length() == 11) {
            return phone.substring(0, 3) + "****" + phone.substring(7);
        }
        return phone;
    }

    private String maskIdCard(String idCard) {
        if (idCard.length() == 18) {
            return idCard.substring(0, 6) + "********" + idCard.substring(14);
        }
        return idCard;
    }

    private String maskEmail(String email) {
        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            String prefix = email.substring(0, atIndex);
            String suffix = email.substring(atIndex);
            if (prefix.length() > 2) {
                return prefix.substring(0, 2) + "***" + suffix;
            }
        }
        return email;
    }

    private String maskName(String name) {
        if (name.length() > 1) {
        }
        return name;
    }

    private String getCurrentUserIP() {
        // 这里应该从请求上下文获取真实IP
        return "127.0.0.1";
    }

    private String getCurrentUserAgent() {
        // 这里应该从请求上下文获取User-Agent
        return "Unknown";
    }

    private java.util.Map<String, String> assessUserRisks() {
        java.util.Map<String, String> riskAssessment = new ConcurrentHashMap<>();
        
        userStats.forEach((userId, stats) -> {
            String riskLevel = "LOW";
            
            if (stats.getHighSensitivityCount() > 1000) {
                riskLevel = "HIGH";
            } else if (stats.getHighSensitivityCount() > 100) {
                riskLevel = "MEDIUM";
            }
            
            riskAssessment.put(userId, riskLevel);
        });
        
        return riskAssessment;
    }

    private java.util.List<String> generateSecurityRecommendations() {
        java.util.List<String> recommendations = new java.util.ArrayList<>();
        
        long totalHighSensitivity = userStats.values().stream()
                .mapToLong(UserOperationStats::getHighSensitivityCount)
                .sum();
        
        if (totalHighSensitivity > 10000) {
            recommendations.add("敏感数据访问量过大，建议加强访问控制和审批流程");
        }
        
        if (anomalyCounter.size() > 10) {
            recommendations.add("检测到多个异常行为，建议进行安全风险评估");
        }
        
        if (accessLog.size() > 100000) {
            recommendations.add("审计日志量过大，建议配置自动归档策略");
        }
        
        return recommendations;
    }

    /**
     * 敏感级别枚举
     */
    public enum SensitivityLevel {
        LOW, MEDIUM, HIGH, CRITICAL
    }

    /**
     * 数据类型枚举
     */
    public enum DataType {
        PHONE, ID_CARD, EMAIL, NAME, ADDRESS, OTHER
    }

    /**
     * 敏感数据访问记录
     */
    @lombok.Builder
    @lombok.Data
    public static class SensitiveDataAccess {
        private String userId;
        private String operation;
        private String dataType;
        private SensitivityLevel sensitivityLevel;
        private LocalDateTime accessTime;
        private String ipAddress;
        private String userAgent;
    }

    /**
     * 用户操作统计
     */
    public static class UserOperationStats {
        private final ConcurrentHashMap<String, AtomicLong> operationCounts = new ConcurrentHashMap<>();
        private final AtomicLong highSensitivityCount = new AtomicLong(0);
        
        public void recordOperation(String operation, SensitivityLevel level) {
            operationCounts.computeIfAbsent(operation, k -> new AtomicLong(0)).incrementAndGet();
            
            if (level == SensitivityLevel.HIGH || level == SensitivityLevel.CRITICAL) {
                highSensitivityCount.incrementAndGet();
            }
        }
        
        public long getOperationCount(String operation) {
            AtomicLong count = operationCounts.get(operation);
            return count != null ? count.get() : 0;
        }
        
        public long getHighSensitivityCount() {
            return highSensitivityCount.get();
        }
    }

    /**
     * 安全审计报告
     */
    @lombok.Data
    public static class SecurityAuditReport {
        private LocalDateTime generatedAt;
        private long totalDataAccess;
        private long highSensitivityAccess;
        private java.util.Map<String, String> userRiskAssessment;
        private java.util.Map<String, AtomicLong> anomalousActivities;
        private java.util.List<String> securityRecommendations;
    }
}