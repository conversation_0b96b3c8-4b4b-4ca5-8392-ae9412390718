package com.yuanshuo.platform.admin.common.core.domain.model.res;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 手机白名单用户信息响应对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhoneWhiteListUserRes {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 认证状态
     */
    private String authStatus;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 平台来源
     */
    private String platform;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 承运开通状态
     */
    private String carrierStatus;

    /**
     * 是否为白名单用户
     */
    private Integer isWhitelist;

    /**
     * 邀请人用户ID
     */
    private Long inviterUserId;

    /**
     * 邀请人姓名
     */
    private String inviterName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 是否找到用户信息
     */
    private Boolean userFound;

    /**
     * 用户不存在时的提示信息
     */
    private String message;
}
