package com.yuanshuo.platform.admin.common.core.domain.model.req;

import lombok.Data;

/**
 * 用户登录方式请求参数对象
 *
 * <AUTHOR>
 */
@Data
public class SysUserAuthReq {

    /** 用户id */
    private Long userId;

    /** 登录类型（Phone/Email/WxMp/AliMp/WeCom/Password） */
    private String authType;

    /** 登录标识（手机号/邮箱/第三方openid） */
    private String identifier;

    /** 状态 1开通 -1禁用 */
    private Integer status;

}
