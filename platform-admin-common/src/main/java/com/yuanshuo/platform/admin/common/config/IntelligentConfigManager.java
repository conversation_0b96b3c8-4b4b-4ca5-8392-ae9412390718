package com.yuanshuo.platform.admin.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class IntelligentConfigManager {

    private final ConcurrentHashMap<String, ConfigItem> configStore = new ConcurrentHashMap<>();
    private final CopyOnWriteArrayList<ConfigChangeListener> listeners = new CopyOnWriteArrayList<>();
    private final ConcurrentHashMap<String, CopyOnWriteArrayList<ConfigChangeRecord>> changeHistory = new ConcurrentHashMap<>();

    public <T> T getConfig(String key, Class<T> type, T defaultValue) {
        ConfigItem item = configStore.get(key);
        if (item == null) {
            log.debug("配置项不存在，使用默认值：key={}, default={}", key, defaultValue);
            return defaultValue;
        }
        
        try {
            return convertValue(item.getValue(), type);
        } catch (Exception e) {
            log.error("配置值转换失败：key={}, value={}, type={}, error={}", 
                    key, item.getValue(), type.getSimpleName(), e.getMessage());
            return defaultValue;
        }
    }

    public <T> boolean setConfig(String key, T value, String operator) {
        try {
            ConfigItem oldItem = configStore.get(key);
            String oldValue = oldItem != null ? oldItem.getValue() : null;
            
            if (!validateConfigValue(key, value)) {
                log.warn("配置值验证失败：key={}, value={}", key, value);
                return false;
            }
            
            ConfigItem newItem = new ConfigItem();
            newItem.setKey(key);
            newItem.setValue(String.valueOf(value));
            newItem.setType(value.getClass().getSimpleName());
            newItem.setLastModified(LocalDateTime.now());
            newItem.setModifiedBy(operator);
            newItem.setVersion(oldItem != null ? oldItem.getVersion() + 1 : 1);
            
            configStore.put(key, newItem);
            
            recordConfigChange(key, oldValue, String.valueOf(value), operator);
            
            notifyConfigChange(key, oldValue, String.valueOf(value));
            
            log.info("配置更新成功：key={}, oldValue={}, newValue={}, operator={}, version={}", 
                    key, oldValue, value, operator, newItem.getVersion());
            
            return true;
            
        } catch (Exception e) {
            log.error("配置更新失败：key={}, value={}, operator={}, error={}", 
                    key, value, operator, e.getMessage(), e);
            return false;
        }
    }

    public boolean setBatchConfig(java.util.Map<String, Object> configs, String operator) {
        java.util.Map<String, String> oldValues = new java.util.HashMap<>();
        java.util.Map<String, String> newValues = new java.util.HashMap<>();
        
        try {
            for (java.util.Map.Entry<String, Object> entry : configs.entrySet()) {
                if (!validateConfigValue(entry.getKey(), entry.getValue())) {
                    log.warn("批量配置验证失败：key={}, value={}", entry.getKey(), entry.getValue());
                    return false;
                }
            }
            
            for (java.util.Map.Entry<String, Object> entry : configs.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                ConfigItem oldItem = configStore.get(key);
                String oldValue = oldItem != null ? oldItem.getValue() : null;
                oldValues.put(key, oldValue);
                newValues.put(key, String.valueOf(value));
                
                ConfigItem newItem = new ConfigItem();
                newItem.setKey(key);
                newItem.setValue(String.valueOf(value));
                newItem.setType(value.getClass().getSimpleName());
                newItem.setLastModified(LocalDateTime.now());
                newItem.setModifiedBy(operator);
                newItem.setVersion(oldItem != null ? oldItem.getVersion() + 1 : 1);
                
                configStore.put(key, newItem);
                recordConfigChange(key, oldValue, String.valueOf(value), operator);
            }
            
            // 批量通知
            oldValues.forEach((key, oldValue) -> {
                notifyConfigChange(key, oldValue, newValues.get(key));
            });
            
            log.info("批量配置更新成功：count={}, operator={}", configs.size(), operator);
            return true;
            
        } catch (Exception e) {
            log.error("批量配置更新失败：operator={}, error={}", operator, e.getMessage(), e);
            return false;
        }
    }

    public boolean rollbackConfig(String key, int targetVersion, String operator) {
        try {
            CopyOnWriteArrayList<ConfigChangeRecord> history = changeHistory.get(key);
            if (history == null || history.isEmpty()) {
                log.warn("配置无变更历史，无法回滚：key={}", key);
                return false;
            }
            
            ConfigChangeRecord targetRecord = null;
            for (ConfigChangeRecord record : history) {
                if (record.getVersion() == targetVersion) {
                    targetRecord = record;
                    break;
                }
            }
            
            if (targetRecord == null) {
                log.warn("目标版本不存在：key={}, version={}", key, targetVersion);
                return false;
            }
            
            return setConfig(key, targetRecord.getNewValue(), operator + "(rollback)");
            
        } catch (Exception e) {
            log.error("配置回滚失败：key={}, version={}, error={}", key, targetVersion, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取配置变更历史
     */
    public java.util.List<ConfigChangeRecord> getConfigHistory(String key) {
        CopyOnWriteArrayList<ConfigChangeRecord> history = changeHistory.get(key);
        return history != null ? new java.util.ArrayList<>(history) : new java.util.ArrayList<>();
    }

    /**
     * 获取所有配置
     */
    public java.util.Map<String, ConfigItem> getAllConfigs() {
        return new java.util.HashMap<>(configStore);
    }

    /**
     * 添加配置变更监听器
     */
    public void addConfigChangeListener(ConfigChangeListener listener) {
        listeners.add(listener);
        log.info("添加配置变更监听器：{}", listener.getClass().getSimpleName());
    }

    /**
     * 移除配置变更监听器
     */
    public void removeConfigChangeListener(ConfigChangeListener listener) {
        listeners.remove(listener);
        log.info("移除配置变更监听器：{}", listener.getClass().getSimpleName());
    }

    /**
     * 导出配置
     */
    public ConfigExport exportConfigs() {
        ConfigExport export = new ConfigExport();
        export.setExportTime(LocalDateTime.now());
        export.setConfigs(new java.util.HashMap<>(configStore));
        export.setTotalCount(configStore.size());
        
        log.info("配置导出完成：count={}", configStore.size());
        return export;
    }

    /**
     * 导入配置
     */
    public boolean importConfigs(ConfigExport configExport, String operator, boolean overwrite) {
        try {
            int importCount = 0;
            int skipCount = 0;
            
            for (java.util.Map.Entry<String, ConfigItem> entry : configExport.getConfigs().entrySet()) {
                String key = entry.getKey();
                ConfigItem importItem = entry.getValue();
                
                if (!overwrite && configStore.containsKey(key)) {
                    skipCount++;
                    continue;
                }
                
                setConfig(key, importItem.getValue(), operator + "(import)");
                importCount++;
            }
            
            log.info("配置导入完成：import={}, skip={}, operator={}", importCount, skipCount, operator);
            return true;
            
        } catch (Exception e) {
            log.error("配置导入失败：operator={}, error={}", operator, e.getMessage(), e);
            return false;
        }
    }

    @SuppressWarnings("unchecked")
    private <T> T convertValue(String value, Class<T> type) {
        if (value == null) {
            return null;
        }
        
        if (type == String.class) {
            return (T) value;
        } else if (type == Integer.class || type == int.class) {
            return (T) Integer.valueOf(value);
        } else if (type == Long.class || type == long.class) {
            return (T) Long.valueOf(value);
        } else if (type == Boolean.class || type == boolean.class) {
            return (T) Boolean.valueOf(value);
        } else if (type == Double.class || type == double.class) {
            return (T) Double.valueOf(value);
        } else {
            throw new IllegalArgumentException("不支持的配置类型：" + type.getSimpleName());
        }
    }

    private boolean validateConfigValue(String key, Object value) {
        // 基本验证
        if (key == null || key.trim().isEmpty()) {
            return false;
        }
        
        if (value == null) {
            return false;
        }
        
        // 特定配置项验证
        if (key.contains("timeout") && value instanceof Number) {
            return ((Number) value).longValue() > 0;
        }
        
        if (key.contains("size") && value instanceof Number) {
            return ((Number) value).longValue() >= 0;
        }
        
        return true;
    }

    private void recordConfigChange(String key, String oldValue, String newValue, String operator) {
        ConfigChangeRecord record = new ConfigChangeRecord();
        record.setKey(key);
        record.setOldValue(oldValue);
        record.setNewValue(newValue);
        record.setOperator(operator);
        record.setChangeTime(LocalDateTime.now());
        
        ConfigItem currentItem = configStore.get(key);
        record.setVersion(currentItem != null ? currentItem.getVersion() : 1);
        
        changeHistory.computeIfAbsent(key, k -> new CopyOnWriteArrayList<>()).add(record);
        
        // 限制历史记录数量
        CopyOnWriteArrayList<ConfigChangeRecord> history = changeHistory.get(key);
        if (history.size() > 100) {
            history.remove(0);
        }
    }

    private void notifyConfigChange(String key, String oldValue, String newValue) {
        for (ConfigChangeListener listener : listeners) {
            try {
                listener.onConfigChange(key, oldValue, newValue);
            } catch (Exception e) {
                log.error("配置变更通知失败：listener={}, key={}, error={}", 
                        listener.getClass().getSimpleName(), key, e.getMessage(), e);
            }
        }
    }

    /**
     * 配置项
     */
    @lombok.Data
    public static class ConfigItem {
        private String key;
        private String value;
        private String type;
        private LocalDateTime lastModified;
        private String modifiedBy;
        private int version;
    }

    /**
     * 配置变更记录
     */
    @lombok.Data
    public static class ConfigChangeRecord {
        private String key;
        private String oldValue;
        private String newValue;
        private String operator;
        private LocalDateTime changeTime;
        private int version;
    }

    /**
     * 配置导出
     */
    @lombok.Data
    public static class ConfigExport {
        private LocalDateTime exportTime;
        private java.util.Map<String, ConfigItem> configs;
        private int totalCount;
    }

    /**
     * 配置变更监听器接口
     */
    public interface ConfigChangeListener {
        void onConfigChange(String key, String oldValue, String newValue);
    }
}