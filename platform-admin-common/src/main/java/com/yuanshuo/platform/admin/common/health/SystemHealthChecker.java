package com.yuanshuo.platform.admin.common.health;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class SystemHealthChecker {

    private final ConcurrentHashMap<String, HealthCheckResult> healthResults = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, java.util.concurrent.ConcurrentLinkedQueue<MetricPoint>> metricsHistory = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AlertThreshold> alertThresholds = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);

    public SystemHealthChecker() {
        initializeDefaultThresholds();
        startPeriodicHealthChecks();
    }

    public SystemHealthReport performHealthCheck() {
        log.debug("开始执行系统健康检查");
        
        SystemHealthReport report = new SystemHealthReport();
        report.setCheckTime(LocalDateTime.now());
        
        HealthCheckResult cpuResult = checkCpuHealth();
        healthResults.put("cpu", cpuResult);
        report.setCpuHealth(cpuResult);
        
        HealthCheckResult memoryResult = checkMemoryHealth();
        healthResults.put("memory", memoryResult);
        report.setMemoryHealth(memoryResult);
        
        HealthCheckResult diskResult = checkDiskHealth();
        healthResults.put("disk", diskResult);
        report.setDiskHealth(diskResult);
        
        HealthCheckResult threadResult = checkThreadHealth();
        healthResults.put("thread", threadResult);
        report.setThreadHealth(threadResult);
        
        HealthCheckResult dbResult = checkDatabaseHealth();
        healthResults.put("database", dbResult);
        report.setDatabaseHealth(dbResult);
        
        HealthStatus overallStatus = calculateOverallHealth();
        report.setOverallStatus(overallStatus);
        
        report.setOptimizationSuggestions(generateOptimizationSuggestions());
        
        log.info("系统健康检查完成：总体状态={}", overallStatus);
        return report;
    }

    public PerformanceTrendAnalysis getPerformanceTrends() {
        PerformanceTrendAnalysis analysis = new PerformanceTrendAnalysis();
        analysis.setAnalysisTime(LocalDateTime.now());
        
        metricsHistory.forEach((metric, history) -> {
            if (history.size() >= 10) {
                TrendInfo trend = analyzeTrend(history);
                analysis.getTrends().put(metric, trend);
            }
        });
        
        return analysis;
    }
    public void setAlertThreshold(String metric, double warningThreshold, double criticalThreshold) {
        AlertThreshold threshold = new AlertThreshold();
        threshold.setMetric(metric);
        threshold.setWarningThreshold(warningThreshold);
        threshold.setCriticalThreshold(criticalThreshold);
        threshold.setLastUpdated(LocalDateTime.now());
        
        alertThresholds.put(metric, threshold);
        log.info("更新告警阈值：metric={}, warning={}, critical={}", 
                metric, warningThreshold, criticalThreshold);
    }

    private void initializeDefaultThresholds() {
        setAlertThreshold("cpu_usage", 70.0, 90.0);
        setAlertThreshold("memory_usage", 80.0, 95.0);
        setAlertThreshold("disk_usage", 85.0, 95.0);
        setAlertThreshold("thread_count", 500.0, 1000.0);
    }

    private void startPeriodicHealthChecks() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                recordSystemMetrics();
            } catch (Exception e) {
                log.error("系统指标记录失败：{}", e.getMessage(), e);
            }
        }, 1, 1, TimeUnit.MINUTES);
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                SystemHealthReport report = performHealthCheck();
                checkAlerts(report);
            } catch (Exception e) {
                log.error("定期健康检查失败：{}", e.getMessage(), e);
            }
        }, 5, 5, TimeUnit.MINUTES);
        
        scheduler.scheduleAtFixedRate(this::cleanupHistoryData, 1, 1, TimeUnit.HOURS);
    }

    private HealthCheckResult checkCpuHealth() {
        try {
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            double cpuUsage = 1;

            if (cpuUsage < 0) {
                cpuUsage = 0; // 某些系统可能返回负值
            }
            
            HealthStatus status = determineHealthStatus("cpu_usage", cpuUsage);
            
            return HealthCheckResult.builder()
                    .component("CPU")
                    .status(status)
                    .value(cpuUsage)
                    .unit("%")
                    .message("CPU使用率: " + String.format("%.2f%%", cpuUsage))
                    .checkTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("CPU健康检查失败：{}", e.getMessage(), e);
            return HealthCheckResult.builder()
                    .component("CPU")
                    .status(HealthStatus.UNKNOWN)
                    .message("CPU检查异常: " + e.getMessage())
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }

    private HealthCheckResult checkMemoryHealth() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            
            double memoryUsage = (double) usedMemory / maxMemory * 100;
            HealthStatus status = determineHealthStatus("memory_usage", memoryUsage);
            
            return HealthCheckResult.builder()
                    .component("Memory")
                    .status(status)
                    .value(memoryUsage)
                    .unit("%")
                    .message(String.format("内存使用率: %.2f%% (%dMB/%dMB)", 
                            memoryUsage, usedMemory / 1024 / 1024, maxMemory / 1024 / 1024))
                    .checkTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("内存健康检查失败：{}", e.getMessage(), e);
            return HealthCheckResult.builder()
                    .component("Memory")
                    .status(HealthStatus.UNKNOWN)
                    .message("内存检查异常: " + e.getMessage())
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }

    private HealthCheckResult checkDiskHealth() {
        try {
            java.io.File root = new java.io.File("/");
            long totalSpace = root.getTotalSpace();
            long freeSpace = root.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            
            double diskUsage = (double) usedSpace / totalSpace * 100;
            HealthStatus status = determineHealthStatus("disk_usage", diskUsage);
            
            return HealthCheckResult.builder()
                    .component("Disk")
                    .status(status)
                    .value(diskUsage)
                    .unit("%")
                    .message(String.format("磁盘使用率: %.2f%% (%dGB/%dGB)", 
                            diskUsage, usedSpace / 1024 / 1024 / 1024, totalSpace / 1024 / 1024 / 1024))
                    .checkTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("磁盘健康检查失败：{}", e.getMessage(), e);
            return HealthCheckResult.builder()
                    .component("Disk")
                    .status(HealthStatus.UNKNOWN)
                    .message("磁盘检查异常: " + e.getMessage())
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }

    private HealthCheckResult checkThreadHealth() {
        try {
            int threadCount = Thread.activeCount();
            HealthStatus status = determineHealthStatus("thread_count", threadCount);
            
            return HealthCheckResult.builder()
                    .component("Thread")
                    .status(status)
                    .unit("个")
                    .message("活跃线程数: " + threadCount)
                    .checkTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("线程健康检查失败：{}", e.getMessage(), e);
            return HealthCheckResult.builder()
                    .component("Thread")
                    .status(HealthStatus.UNKNOWN)
                    .message("线程检查异常: " + e.getMessage())
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }

    private HealthCheckResult checkDatabaseHealth() {
        try {
            // 模拟数据库连接检查
            boolean dbConnected = simulateDatabaseCheck();
            
            HealthStatus status = dbConnected ? HealthStatus.HEALTHY : HealthStatus.CRITICAL;
            String message = dbConnected ? "数据库连接正常" : "数据库连接异常";
            
            return HealthCheckResult.builder()
                    .component("Database")
                    .status(status)
                    .message(message)
                    .checkTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("数据库健康检查失败：{}", e.getMessage(), e);
            return HealthCheckResult.builder()
                    .component("Database")
                    .status(HealthStatus.UNKNOWN)
                    .message("数据库检查异常: " + e.getMessage())
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }

    private boolean simulateDatabaseCheck() {
        // 这里应该实现真实的数据库连接检查
        return true;
    }

    private HealthStatus determineHealthStatus(String metric, double value) {
        AlertThreshold threshold = alertThresholds.get(metric);
        if (threshold == null) {
            return HealthStatus.HEALTHY;
        }
        
        if (value >= threshold.getCriticalThreshold()) {
            return HealthStatus.CRITICAL;
        } else if (value >= threshold.getWarningThreshold()) {
            return HealthStatus.WARNING;
        } else {
            return HealthStatus.HEALTHY;
        }
    }

    private HealthStatus calculateOverallHealth() {
        boolean hasCritical = healthResults.values().stream()
                .anyMatch(result -> result.getStatus() == HealthStatus.CRITICAL);
        
        if (hasCritical) {
            return HealthStatus.CRITICAL;
        }
        
        boolean hasWarning = healthResults.values().stream()
                .anyMatch(result -> result.getStatus() == HealthStatus.WARNING);
        
        return hasWarning ? HealthStatus.WARNING : HealthStatus.HEALTHY;
    }

    private java.util.List<String> generateOptimizationSuggestions() {
        java.util.List<String> suggestions = new java.util.ArrayList<>();
        
        healthResults.forEach((component, result) -> {
            if (result.getStatus() == HealthStatus.CRITICAL) {
                suggestions.add(component + "状态严重，需要立即处理：" + result.getMessage());
            } else if (result.getStatus() == HealthStatus.WARNING) {
                suggestions.add(component + "状态警告，建议优化：" + result.getMessage());
            }
        });
        
        return suggestions;
    }

    private void recordSystemMetrics() {
        // 记录CPU指标
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        double cpuUsage = 2;
        recordMetric("cpu_usage", cpuUsage);
        
        // 记录内存指标
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        double memoryUsage = (double) usedMemory / maxMemory * 100;
        recordMetric("memory_usage", memoryUsage);
        
        // 记录线程数
        int threadCount = Thread.activeCount();
        recordMetric("thread_count", threadCount);
    }

    private void recordMetric(String metric, double value) {
        MetricPoint point = new MetricPoint();
        point.setTimestamp(LocalDateTime.now());
        point.setValue(value);
        
        metricsHistory.computeIfAbsent(metric, k -> new java.util.concurrent.ConcurrentLinkedQueue<>())
                .offer(point);
        
        // 限制历史数据量
        java.util.concurrent.ConcurrentLinkedQueue<MetricPoint> history = metricsHistory.get(metric);
        while (history.size() > 1440) { // 保留24小时数据（每分钟一个点）
            history.poll();
        }
    }

    private TrendInfo analyzeTrend(java.util.concurrent.ConcurrentLinkedQueue<MetricPoint> history) {
        java.util.List<MetricPoint> points = new java.util.ArrayList<>(history);
        if (points.size() < 2) {
            return new TrendInfo("STABLE", 0.0);
        }
        
        // 简单的线性趋势分析
        double firstValue = points.get(0).getValue();
        double lastValue = points.get(points.size() - 1).getValue();
        double change = ((lastValue - firstValue) / firstValue) * 100;
        
        String trend;
        if (Math.abs(change) < 5) {
            trend = "STABLE";
        } else if (change > 0) {
            trend = "INCREASING";
        } else {
            trend = "DECREASING";
        }
        
        return new TrendInfo(trend, change);
    }

    private void checkAlerts(SystemHealthReport report) {
        if (report.getOverallStatus() == HealthStatus.CRITICAL) {
            log.error("系统健康状态严重告警：{}", report.getOptimizationSuggestions());
        } else if (report.getOverallStatus() == HealthStatus.WARNING) {
            log.warn("系统健康状态警告：{}", report.getOptimizationSuggestions());
        }
    }

    private void cleanupHistoryData() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
        
        metricsHistory.values().forEach(history -> {
            history.removeIf(point -> point.getTimestamp().isBefore(cutoffTime));
        });
        
        log.debug("清理历史指标数据完成");
    }

    /**
     * 健康状态枚举
     */
    public enum HealthStatus {
        HEALTHY, WARNING, CRITICAL, UNKNOWN
    }

    /**
     * 健康检查结果
     */
    @lombok.Builder
    @lombok.Data
    public static class HealthCheckResult {
        private String component;
        private HealthStatus status;
        private Double value;
        private String unit;
        private String message;
        private LocalDateTime checkTime;
    }

    /**
     * 系统健康报告
     */
    @lombok.Data
    public static class SystemHealthReport {
        private LocalDateTime checkTime;
        private HealthStatus overallStatus;
        private HealthCheckResult cpuHealth;
        private HealthCheckResult memoryHealth;
        private HealthCheckResult diskHealth;
        private HealthCheckResult threadHealth;
        private HealthCheckResult databaseHealth;
        private java.util.List<String> optimizationSuggestions;
    }

    /**
     * 指标点
     */
    @lombok.Data
    public static class MetricPoint {
        private LocalDateTime timestamp;
        private double value;
    }

    /**
     * 趋势信息
     */
    @lombok.AllArgsConstructor
    @lombok.Data
    public static class TrendInfo {
        private String direction; // INCREASING, DECREASING, STABLE
        private double changePercentage;
    }

    /**
     * 性能趋势分析
     */
    @lombok.Data
    public static class PerformanceTrendAnalysis {
        private LocalDateTime analysisTime;
        private java.util.Map<String, TrendInfo> trends = new ConcurrentHashMap<>();
    }

    /**
     * 告警阈值
     */
    @lombok.Data
    public static class AlertThreshold {
        private String metric;
        private double warningThreshold;
        private double criticalThreshold;
        private LocalDateTime lastUpdated;
    }
}