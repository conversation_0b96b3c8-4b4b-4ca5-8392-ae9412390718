package com.yuanshuo.platform.admin.common.qywx.config;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Data
@ConfigurationProperties(prefix = "wechat.cp")
@RefreshScope
public class WxCpProperties {
    /**
     * 重定向域名
     */
    private String domain;
    /**
     * 设置企业微信的corpId
     */
    private String corpId;

    private List<AppConfig> appConfigs;

    /**
     * 重定向地址
     */
    private List<RedirectUrl> redirectUrls;

    @Getter
    @Setter
    public static class AppConfig {
        /**
         * 设置企业微信应用的AgentId
         */
        private Integer agentId;

        /**
         * 设置企业微信应用的Secret
         */
        private String secret;

        /**
         * 设置企业微信应用的token
         */
        private String token;

        /**
         * 设置企业微信应用的EncodingAESKey
         */
        private String aesKey;

    }

    @Getter
    @Setter
    public static class RedirectUrl {
        /**
         * 重定向地址
         */
        private String url;

        /**
         * 重定向地址对应的环境
         */
        private String env;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
