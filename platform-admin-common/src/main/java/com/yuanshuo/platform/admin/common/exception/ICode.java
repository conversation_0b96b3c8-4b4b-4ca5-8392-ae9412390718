package com.yuanshuo.platform.admin.common.exception;

import java.io.Serializable;

public interface ICode<K> extends Serializable {
    /**
     * 获取code值
     *
     * @return code值
     */
    K getCode();

    /**
     * 获取value值
     *
     * @return value值
     */
    String getValue();

    default BusinessException exp() {
        return new BusinessException(this, getValue());
    }

    default BusinessException exp(Exception e) {
        return new BusinessException(this, getValue(), e);
    }

    default BusinessException exp(String... param) {
        return new BusinessException(this, String.format(getValue(), param));
    }

    default BusinessException exp(String param) {
        return new BusinessException(this, param);
    }
}
