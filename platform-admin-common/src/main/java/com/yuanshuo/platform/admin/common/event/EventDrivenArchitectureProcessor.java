package com.yuanshuo.platform.admin.common.event;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class EventDrivenArchitectureProcessor {

    private final ConcurrentHashMap<String, EventStream> eventStreams = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, EventHandler> eventHandlers = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Saga> sagas = new ConcurrentHashMap<>();
    private final EventSourcing eventSourcing = new EventSourcing();
    private final CQRS cqrs = new CQRS();
    private final AtomicLong eventCounter = new AtomicLong(0);

    public EventProcessingResult publishEvent(DomainEvent event) {
        long eventId = eventCounter.incrementAndGet();
        long startTime = System.nanoTime();
        
        event.setEventId(eventId);
        event.setTimestamp(LocalDateTime.now());
        
        log.info("发布领域事件：eventType={}, eventId={}, aggregateId={}", 
                event.getEventType(), eventId, event.getAggregateId());
        
        try {
            eventSourcing.appendEvent(event);
            
            EventStream stream = getOrCreateEventStream(event.getStreamId());
            stream.appendEvent(event);
            
            java.util.List<CompletableFuture<HandlerResult>> futures = new java.util.ArrayList<>();
            
            for (EventHandler handler : findHandlers(event.getEventType())) {
                CompletableFuture<HandlerResult> future = CompletableFuture.supplyAsync(() -> {
                    return processEventWithHandler(event, handler);
                });
                futures.add(future);
            }
            
            processSagas(event);
            
            CompletableFuture<Void> allHandlers = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));
            
            allHandlers.get(5, java.util.concurrent.TimeUnit.SECONDS);
            
            long processingTime = System.nanoTime() - startTime;
            
            return EventProcessingResult.builder()
                    .eventId(eventId)
                    .eventType(event.getEventType())
                    .handlersExecuted(futures.size())
                    .sagasTriggered(countTriggeredSagas(event))
                    .processingTimeNanos(processingTime)
                    .processedAt(LocalDateTime.now())
                    .status(ProcessingStatus.SUCCESS)
                    .build();
                    
        } catch (Exception e) {
            log.error("事件处理失败：eventType={}, eventId={}, error={}", 
                    event.getEventType(), eventId, e.getMessage(), e);
            
            return EventProcessingResult.builder()
                    .eventId(eventId)
                    .eventType(event.getEventType())
                    .status(ProcessingStatus.FAILED)
                    .errorMessage(e.getMessage())
                    .processedAt(LocalDateTime.now())
                    .build();
        }
    }

    public void registerEventHandler(String eventType, EventHandler handler) {
        eventHandlers.put(eventType + ":" + handler.getHandlerId(), handler);
        
        log.info("事件处理器注册成功：eventType={}, handlerId={}", eventType, handler.getHandlerId());
    }

    public void registerSaga(String sagaId, Saga saga) {
        sagas.put(sagaId, saga);
        
        log.info("Saga注册成功：sagaId={}, steps={}", sagaId, saga.getSteps().size());
    }

    public EventReplayResult replayEvents(String streamId, LocalDateTime fromTime, LocalDateTime toTime) {
        log.info("开始事件重放：streamId={}, from={}, to={}", streamId, fromTime, toTime);
        
        EventStream stream = eventStreams.get(streamId);
        if (stream == null) {
            throw new EventStreamNotFoundException("Event stream not found: " + streamId);
        }
        
        java.util.List<DomainEvent> eventsToReplay = stream.getEvents().stream()
                .filter(event -> event.getTimestamp().isAfter(fromTime) && event.getTimestamp().isBefore(toTime))
                .collect(java.util.stream.Collectors.toList());
        
        int replayedCount = 0;
        int failedCount = 0;
        
        for (DomainEvent event : eventsToReplay) {
            try {
                EventProcessingResult result = publishEvent(event);
                if (result.getStatus() == ProcessingStatus.SUCCESS) {
                    replayedCount++;
                } else {
                    failedCount++;
                }
            } catch (Exception e) {
                failedCount++;
                log.error("事件重放失败：eventId={}, error={}", event.getEventId(), e.getMessage());
            }
        }
        
        return EventReplayResult.builder()
                .streamId(streamId)
                .totalEvents(eventsToReplay.size())
                .replayedEvents(replayedCount)
                .failedEvents(failedCount)
                .replayedAt(LocalDateTime.now())
                .build();
    }

    private EventStream getOrCreateEventStream(String streamId) {
        return eventStreams.computeIfAbsent(streamId, k -> new EventStream(k));
    }

    private java.util.List<EventHandler> findHandlers(String eventType) {
        return eventHandlers.values().stream()
                .filter(handler -> handler.canHandle(eventType))
                .collect(java.util.stream.Collectors.toList());
    }

    private HandlerResult processEventWithHandler(DomainEvent event, EventHandler handler) {
        long startTime = System.nanoTime();
        
        try {
            handler.handle(event);
            long processingTime = System.nanoTime() - startTime;
            
            return HandlerResult.builder()
                    .handlerId(handler.getHandlerId())
                    .eventId(event.getEventId())
                    .status(ProcessingStatus.SUCCESS)
                    .processingTimeNanos(processingTime)
                    .processedAt(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            long processingTime = System.nanoTime() - startTime;
            
            return HandlerResult.builder()
                    .handlerId(handler.getHandlerId())
                    .eventId(event.getEventId())
                    .status(ProcessingStatus.FAILED)
                    .errorMessage(e.getMessage())
                    .processingTimeNanos(processingTime)
                    .processedAt(LocalDateTime.now())
                    .build();
        }
    }

    private void processSagas(DomainEvent event) {
        for (Saga saga : sagas.values()) {
            if (saga.isTriggeredBy(event)) {
                CompletableFuture.runAsync(() -> {
                    try {
                        saga.process(event);
                    } catch (Exception e) {
                        log.error("Saga处理失败：sagaId={}, eventId={}, error={}", 
                                saga.getSagaId(), event.getEventId(), e.getMessage(), e);
                    }
                });
            }
        }
    }

    private int countTriggeredSagas(DomainEvent event) {
        return (int) sagas.values().stream()
                .filter(saga -> saga.isTriggeredBy(event))
                .count();
    }

    public static class EventSourcing {
        private final ConcurrentHashMap<String, EventStore> eventStores = new ConcurrentHashMap<>();

        public void appendEvent(DomainEvent event) {
            EventStore store = eventStores.computeIfAbsent(event.getAggregateId(), k -> new EventStore(k));
            store.appendEvent(event);
        }

        public java.util.List<DomainEvent> getEvents(String aggregateId) {
            EventStore store = eventStores.get(aggregateId);
            return store != null ? store.getEvents() : new java.util.ArrayList<>();
        }

        public Snapshot createSnapshot(String aggregateId) {
            java.util.List<DomainEvent> events = getEvents(aggregateId);
            
            Snapshot snapshot = new Snapshot();
            snapshot.setAggregateId(aggregateId);
            snapshot.setVersion(events.size());
            snapshot.setData(aggregateId + "_snapshot_data");
            snapshot.setCreatedAt(LocalDateTime.now());
            
            return snapshot;
        }
    }

    public static class CQRS {
        private final ConcurrentHashMap<String, CommandHandler> commandHandlers = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, QueryHandler> queryHandlers = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, ReadModel> readModels = new ConcurrentHashMap<>();

        public CommandResult executeCommand(Command command) {
            CommandHandler handler = commandHandlers.get(command.getCommandType());
            if (handler == null) {
                throw new CommandHandlerNotFoundException("No handler for command: " + command.getCommandType());
            }
            
            return handler.handle(command);
        }

        public QueryResult executeQuery(Query query) {
            QueryHandler handler = queryHandlers.get(query.getQueryType());
            if (handler == null) {
                throw new QueryHandlerNotFoundException("No handler for query: " + query.getQueryType());
            }
            
            return handler.handle(query);
        }

        public void updateReadModel(String modelId, DomainEvent event) {
            ReadModel model = readModels.computeIfAbsent(modelId, k -> new ReadModel(k));
            model.applyEvent(event);
        }
    }

    @lombok.Data
    public static class EventStream {
        private String streamId;
        private CopyOnWriteArrayList<DomainEvent> events = new CopyOnWriteArrayList<>();
        private long version = 0;
        private LocalDateTime createdAt = LocalDateTime.now();

        public EventStream(String streamId) {
            this.streamId = streamId;
        }

        public void appendEvent(DomainEvent event) {
            events.add(event);
            version++;
        }
    }

    @lombok.Data
    public static class DomainEvent {
        private long eventId;
        private String eventType;
        private String aggregateId;
        private String streamId;
        private java.util.Map<String, Object> data = new ConcurrentHashMap<>();
        private LocalDateTime timestamp;
        private String userId;
        private long version;
    }

    public interface EventHandler {
        String getHandlerId();
        boolean canHandle(String eventType);
        void handle(DomainEvent event);
    }

    @lombok.Data
    public static class Saga {
        private String sagaId;
        private String name;
        private java.util.List<SagaStep> steps = new java.util.ArrayList<>();
        private SagaState state = SagaState.PENDING;
        private LocalDateTime createdAt = LocalDateTime.now();

        public boolean isTriggeredBy(DomainEvent event) {
            return steps.stream().anyMatch(step -> step.getTriggerEventType().equals(event.getEventType()));
        }

        public void process(DomainEvent event) {
            for (SagaStep step : steps) {
                if (step.getTriggerEventType().equals(event.getEventType())) {
                    step.execute(event);
                }
            }
        }
    }

    @lombok.Data
    public static class SagaStep {
        private String stepId;
        private String triggerEventType;
        private String compensationEventType;
        private StepStatus status = StepStatus.PENDING;

        public void execute(DomainEvent event) {
            try {
                Thread.sleep(ThreadLocalRandom.current().nextInt(10, 100));
                status = StepStatus.COMPLETED;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                status = StepStatus.FAILED;
            }
        }
    }

    @lombok.Data
    public static class EventStore {
        private String aggregateId;
        private CopyOnWriteArrayList<DomainEvent> events = new CopyOnWriteArrayList<>();
        private long version = 0;

        public EventStore(String aggregateId) {
            this.aggregateId = aggregateId;
        }

        public void appendEvent(DomainEvent event) {
            events.add(event);
            version++;
        }
    }

    @lombok.Data
    public static class Snapshot {
        private String aggregateId;
        private long version;
        private String data;
        private LocalDateTime createdAt;
    }

    @lombok.Data
    public static class Command {
        private String commandId;
        private String commandType;
        private String aggregateId;
        private java.util.Map<String, Object> parameters = new ConcurrentHashMap<>();
        private LocalDateTime createdAt = LocalDateTime.now();
    }

    @lombok.Data
    public static class Query {
        private String queryId;
        private String queryType;
        private java.util.Map<String, Object> parameters = new ConcurrentHashMap<>();
        private LocalDateTime createdAt = LocalDateTime.now();
    }

    public interface CommandHandler {
        CommandResult handle(Command command);
    }

    public interface QueryHandler {
        QueryResult handle(Query query);
    }

    @lombok.Data
    public static class ReadModel {
        private String modelId;
        private java.util.Map<String, Object> data = new ConcurrentHashMap<>();
        private long version = 0;
        private LocalDateTime lastUpdated = LocalDateTime.now();

        public ReadModel(String modelId) {
            this.modelId = modelId;
        }

        public void applyEvent(DomainEvent event) {
            data.putAll(event.getData());
            version++;
            lastUpdated = LocalDateTime.now();
        }
    }

    @lombok.Builder
    @lombok.Data
    public static class EventProcessingResult {
        private long eventId;
        private String eventType;
        private int handlersExecuted;
        private int sagasTriggered;
        private long processingTimeNanos;
        private LocalDateTime processedAt;
        private ProcessingStatus status;
        private String errorMessage;
    }

    @lombok.Builder
    @lombok.Data
    public static class HandlerResult {
        private String handlerId;
        private long eventId;
        private ProcessingStatus status;
        private String errorMessage;
        private long processingTimeNanos;
        private LocalDateTime processedAt;
    }

    @lombok.Builder
    @lombok.Data
    public static class EventReplayResult {
        private String streamId;
        private int totalEvents;
        private int replayedEvents;
        private int failedEvents;
        private LocalDateTime replayedAt;
    }

    @lombok.Data
    public static class CommandResult {
        private String commandId;
        private ProcessingStatus status;
        private String result;
        private String errorMessage;
        private LocalDateTime processedAt = LocalDateTime.now();
    }

    @lombok.Data
    public static class QueryResult {
        private String queryId;
        private Object data;
        private int resultCount;
        private LocalDateTime processedAt = LocalDateTime.now();
    }

    public enum ProcessingStatus {
        SUCCESS, FAILED, TIMEOUT, CANCELLED
    }

    public enum SagaState {
        PENDING, RUNNING, COMPLETED, FAILED, COMPENSATING
    }

    public enum StepStatus {
        PENDING, RUNNING, COMPLETED, FAILED, COMPENSATED
    }

    public static class EventStreamNotFoundException extends RuntimeException {
        public EventStreamNotFoundException(String message) {
            super(message);
        }
    }

    public static class CommandHandlerNotFoundException extends RuntimeException {
        public CommandHandlerNotFoundException(String message) {
            super(message);
        }
    }

    public static class QueryHandlerNotFoundException extends RuntimeException {
        public QueryHandlerNotFoundException(String message) {
            super(message);
        }
    }
}