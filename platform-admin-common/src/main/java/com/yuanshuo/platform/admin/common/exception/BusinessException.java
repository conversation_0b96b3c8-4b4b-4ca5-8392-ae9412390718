package com.yuanshuo.platform.admin.common.exception;

import lombok.Data;

@Data
public class BusinessException extends RuntimeException {

    private ICode<Integer> code;

    private String message;

    private Throwable source;

    public BusinessException(String message) {
        super(message);
        this.code = ServerErrorCode.ERROR;
        this.message = message;
    }

    public BusinessException(ICode code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(ICode code, String message, Throwable e) {
        super(message);
        this.code = code;
        this.message = message;
        this.source = e;
    }
}
