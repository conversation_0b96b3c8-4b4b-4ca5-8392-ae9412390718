package com.yuanshuo.platform.admin.common.lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class DistributedLockManager {

    private final ConcurrentHashMap<String, LockInfo> lockRegistry = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, LockStatistics> lockStats = new ConcurrentHashMap<>();
    private final AtomicLong lockOperationCounter = new AtomicLong(0);

    public LockHandle tryLock(String lockKey, long timeout, long leaseTime) {
        long operationId = lockOperationCounter.incrementAndGet();
        long startTime = System.currentTimeMillis();
        
        log.debug("尝试获取分布式锁：key={}, timeout={}s, lease={}s, operationId={}", 
                lockKey, timeout, leaseTime, operationId);
        
        try {
            // 检查锁是否已存在
            LockInfo existingLock = lockRegistry.get(lockKey);
            if (existingLock != null && !existingLock.isExpired()) {
                // 检查是否为同一线程的重入锁
                if (existingLock.getThreadId() == Thread.currentThread().getId()) {
                    existingLock.incrementReentrantCount();
                    log.debug("重入锁获取成功：key={}, reentrantCount={}", lockKey, existingLock.getReentrantCount());
                    return new LockHandle(lockKey, operationId, true);
                } else {
                    log.debug("锁已被其他线程持有：key={}, holder={}", lockKey, existingLock.getThreadId());
                    recordLockStatistics(lockKey, false, System.currentTimeMillis() - startTime);
                    return null;
                }
            }
            
            // 创建新锁
            LockInfo lockInfo = new LockInfo();
            lockInfo.setLockKey(lockKey);
            lockInfo.setThreadId(Thread.currentThread().getId());
            lockInfo.setThreadName(Thread.currentThread().getName());
            lockInfo.setAcquiredAt(LocalDateTime.now());
            lockInfo.setLeaseTime(leaseTime);
            lockInfo.setOperationId(operationId);
            
            // 原子性地尝试获取锁
            LockInfo previousLock = lockRegistry.putIfAbsent(lockKey, lockInfo);
            if (previousLock == null) {
                // 获取锁成功
                long duration = System.currentTimeMillis() - startTime;
                log.info("分布式锁获取成功：key={}, thread={}, duration={}ms, operationId={}", 
                        lockKey, Thread.currentThread().getName(), duration, operationId);
                
                recordLockStatistics(lockKey, true, duration);
                scheduleAutoRelease(lockKey, leaseTime);
                
                return new LockHandle(lockKey, operationId, false);
            } else {
                // 锁已被其他线程获取
                log.debug("锁获取失败，已被其他线程持有：key={}", lockKey);
                recordLockStatistics(lockKey, false, System.currentTimeMillis() - startTime);
                return null;
            }
            
        } catch (Exception e) {
            log.error("获取分布式锁异常：key={}, operationId={}, error={}", lockKey, operationId, e.getMessage(), e);
            recordLockStatistics(lockKey, false, System.currentTimeMillis() - startTime);
            return null;
        }
    }

    /**
     * 释放分布式锁
     */
    public boolean releaseLock(LockHandle handle) {
        if (handle == null) {
            return false;
        }
        
        String lockKey = handle.getLockKey();
        long operationId = handle.getOperationId();
        
        log.debug("尝试释放分布式锁：key={}, operationId={}", lockKey, operationId);
        
        try {
            LockInfo lockInfo = lockRegistry.get(lockKey);
            if (lockInfo == null) {
                log.warn("锁不存在或已被释放：key={}, operationId={}", lockKey, operationId);
                return false;
            }
            
            // 验证锁的所有者
            if (lockInfo.getThreadId() != Thread.currentThread().getId()) {
                log.warn("尝试释放非当前线程持有的锁：key={}, holder={}, current={}", 
                        lockKey, lockInfo.getThreadId(), Thread.currentThread().getId());
                return false;
            }
            
            // 处理重入锁
            if (handle.isReentrant() && lockInfo.getReentrantCount() > 1) {
                lockInfo.decrementReentrantCount();
                log.debug("重入锁计数减少：key={}, reentrantCount={}", lockKey, lockInfo.getReentrantCount());
                return true;
            }
            
            // 释放锁
            lockRegistry.remove(lockKey);
            
            long holdDuration = java.time.Duration.between(lockInfo.getAcquiredAt(), LocalDateTime.now()).toMillis();
            log.info("分布式锁释放成功：key={}, holdDuration={}ms, operationId={}", 
                    lockKey, holdDuration, operationId);
            
            // 更新统计信息
            updateLockHoldStatistics(lockKey, holdDuration);
            
            return true;
            
        } catch (Exception e) {
            log.error("释放分布式锁异常：key={}, operationId={}, error={}", lockKey, operationId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取锁统计报告
     */
    public LockStatisticsReport getLockStatisticsReport() {
        LockStatisticsReport report = new LockStatisticsReport();
        report.setGeneratedAt(LocalDateTime.now());
        report.setTotalLockOperations(lockOperationCounter.get());
        report.setActiveLocks(lockRegistry.size());
        
        // 统计各锁的性能数据
        ConcurrentHashMap<String, LockStatistics> statsSnapshot = new ConcurrentHashMap<>(lockStats);
        report.setLockStatistics(statsSnapshot);
        
        // 计算总体统计
        long totalAttempts = statsSnapshot.values().stream().mapToLong(LockStatistics::getTotalAttempts).sum();
        long successfulAttempts = statsSnapshot.values().stream().mapToLong(LockStatistics::getSuccessfulAttempts).sum();
        
        report.setTotalLockAttempts(totalAttempts);
        report.setSuccessfulLockAttempts(successfulAttempts);
        report.setLockSuccessRate(totalAttempts > 0 ? (double) successfulAttempts / totalAttempts : 0.0);
        
        // 性能建议
        report.setPerformanceRecommendations(generateLockRecommendations(statsSnapshot));
        
        return report;
    }

    /**
     * 清理过期锁
     */
    public void cleanupExpiredLocks() {
        int cleanedCount = 0;
        
        for (java.util.Iterator<java.util.Map.Entry<String, LockInfo>> iterator = lockRegistry.entrySet().iterator(); 
             iterator.hasNext();) {
            java.util.Map.Entry<String, LockInfo> entry = iterator.next();
            LockInfo lockInfo = entry.getValue();
            
            if (lockInfo.isExpired()) {
                iterator.remove();
                cleanedCount++;
                log.info("清理过期锁：key={}, expiredAt={}", entry.getKey(), lockInfo.getExpiredAt());
            }
        }
        
        if (cleanedCount > 0) {
            log.info("锁清理完成，清理数量：{}", cleanedCount);
        }
    }

    private void recordLockStatistics(String lockKey, boolean success, long duration) {
        LockStatistics stats = lockStats.computeIfAbsent(lockKey, k -> new LockStatistics());
        stats.recordAttempt(success, duration);
    }

    private void updateLockHoldStatistics(String lockKey, long holdDuration) {
        LockStatistics stats = lockStats.get(lockKey);
        if (stats != null) {
            stats.recordHoldDuration(holdDuration);
        }
    }

    private void scheduleAutoRelease(String lockKey, long leaseTime) {
        // 模拟自动释放机制
        java.util.concurrent.Executors.newSingleThreadScheduledExecutor().schedule(() -> {
            LockInfo lockInfo = lockRegistry.get(lockKey);
            if (lockInfo != null && lockInfo.isExpired()) {
                lockRegistry.remove(lockKey);
                log.warn("自动释放过期锁：key={}", lockKey);
            }
        }, leaseTime + 1, TimeUnit.SECONDS);
    }

    private java.util.List<String> generateLockRecommendations(ConcurrentHashMap<String, LockStatistics> stats) {
        java.util.List<String> recommendations = new java.util.ArrayList<>();
        
        stats.forEach((lockKey, stat) -> {
            if (stat.getSuccessRate() < 0.8 && stat.getTotalAttempts() > 100) {
                recommendations.add("锁 " + lockKey + " 成功率较低(" + String.format("%.2f%%", stat.getSuccessRate() * 100) + ")，建议优化锁粒度");
            }
            
            if (stat.getAverageHoldTime() > 10000) { // 10秒
                recommendations.add("锁 " + lockKey + " 平均持有时间过长(" + stat.getAverageHoldTime() + "ms)，建议优化业务逻辑");
            }
        });
        
        return recommendations;
    }

    /**
     * 锁信息
     */
    @lombok.Data
    public static class LockInfo {
        private String lockKey;
        private long threadId;
        private String threadName;
        private LocalDateTime acquiredAt;
        private long leaseTime; // 秒
        private long operationId;
        private int reentrantCount = 1;
        
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(acquiredAt.plusSeconds(leaseTime));
        }
        
        public LocalDateTime getExpiredAt() {
            return acquiredAt.plusSeconds(leaseTime);
        }
        
        public void incrementReentrantCount() {
            reentrantCount++;
        }
        
        public void decrementReentrantCount() {
            reentrantCount--;
        }
    }

    /**
     * 锁句柄
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class LockHandle {
        private String lockKey;
        private long operationId;
        private boolean reentrant;
    }

    /**
     * 锁统计信息
     */
    public static class LockStatistics {
        private final AtomicLong totalAttempts = new AtomicLong(0);
        private final AtomicLong successfulAttempts = new AtomicLong(0);
        private final AtomicLong totalAcquireTime = new AtomicLong(0);
        private final AtomicLong totalHoldTime = new AtomicLong(0);
        private final AtomicLong holdCount = new AtomicLong(0);
        
        public void recordAttempt(boolean success, long acquireTime) {
            totalAttempts.incrementAndGet();
            totalAcquireTime.addAndGet(acquireTime);
            
            if (success) {
                successfulAttempts.incrementAndGet();
            }
        }
        
        public void recordHoldDuration(long holdTime) {
            totalHoldTime.addAndGet(holdTime);
            holdCount.incrementAndGet();
        }
        
        public long getTotalAttempts() { return totalAttempts.get(); }
        public long getSuccessfulAttempts() { return successfulAttempts.get(); }
        public double getSuccessRate() { 
            long total = totalAttempts.get();
            return total > 0 ? (double) successfulAttempts.get() / total : 0.0; 
        }
        public double getAverageAcquireTime() { 
            long total = totalAttempts.get();
            return total > 0 ? (double) totalAcquireTime.get() / total : 0.0; 
        }
        public double getAverageHoldTime() { 
            long count = holdCount.get();
            return count > 0 ? (double) totalHoldTime.get() / count : 0.0; 
        }
    }

    /**
     * 锁统计报告
     */
    @lombok.Data
    public static class LockStatisticsReport {
        private LocalDateTime generatedAt;
        private long totalLockOperations;
        private int activeLocks;
        private long totalLockAttempts;
        private long successfulLockAttempts;
        private double lockSuccessRate;
        private ConcurrentHashMap<String, LockStatistics> lockStatistics;
        private java.util.List<String> performanceRecommendations;
    }
}