package com.yuanshuo.platform.admin.common.ai;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Slf4j
public class IntelligentRecommendationEngine {

    private final ConcurrentHashMap<String, UserBehaviorProfile> userProfiles = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, RecommendationModel> models = new ConcurrentHashMap<>();
    private final NeuralNetworkProcessor neuralProcessor = new NeuralNetworkProcessor();
    private final MachineLearningOptimizer mlOptimizer = new MachineLearningOptimizer();

    public RecommendationResult generateRecommendations(String userId, RecommendationContext context) {
        long startTime = System.nanoTime();
        
        UserBehaviorProfile profile = getUserProfile(userId);
        RecommendationModel model = getOptimalModel(profile);
        
        java.util.List<RecommendationItem> items = neuralProcessor.processRecommendations(profile, model, context);
        items = mlOptimizer.optimizeResults(items, profile.getPreferences());
        
        double confidence = calculateConfidenceScore(items, profile);
        long processingTime = System.nanoTime() - startTime;
        
        log.info("AI推荐生成完成：用户={}, 推荐数量={}, 置信度={:.3f}, 耗时={}ns", 
                userId, items.size(), confidence, processingTime);
        
        return RecommendationResult.builder()
                .userId(userId)
                .items(items)
                .confidence(confidence)
                .modelVersion(model.getVersion())
                .processingTimeNanos(processingTime)
                .generatedAt(LocalDateTime.now())
                .build();
    }

    public void updateUserBehavior(String userId, BehaviorEvent event) {
        UserBehaviorProfile profile = userProfiles.computeIfAbsent(userId, k -> new UserBehaviorProfile(userId));
        profile.addBehaviorEvent(event);
        
        if (profile.getEventCount() % 100 == 0) {
            retrainUserModel(userId, profile);
        }
    }

    private UserBehaviorProfile getUserProfile(String userId) {
        return userProfiles.computeIfAbsent(userId, k -> {
            UserBehaviorProfile profile = new UserBehaviorProfile(userId);
            profile.initializeWithDefaults();
            return profile;
        });
    }

    private RecommendationModel getOptimalModel(UserBehaviorProfile profile) {
        String modelKey = determineModelType(profile);
        return models.computeIfAbsent(modelKey, k -> {
            RecommendationModel model = new RecommendationModel(k);
            model.trainWithProfile(profile);
            return model;
        });
    }

    private String determineModelType(UserBehaviorProfile profile) {
        if (profile.getEngagementScore() > 0.8) {
            return "high_engagement_model";
        } else if (profile.getActivityLevel() > 0.6) {
            return "active_user_model";
        } else {
            return "standard_model";
        }
    }

    private double calculateConfidenceScore(java.util.List<RecommendationItem> items, UserBehaviorProfile profile) {
        return items.stream()
                .mapToDouble(item -> item.getScore() * profile.getRelevanceWeight(item.getCategory()))
                .average()
                .orElse(0.0);
    }

    private void retrainUserModel(String userId, UserBehaviorProfile profile) {
        log.info("开始重训练用户模型：userId={}, 事件数量={}", userId, profile.getEventCount());
        
        String modelKey = determineModelType(profile);
        RecommendationModel model = models.get(modelKey);
        if (model != null) {
            model.incrementalTrain(profile.getRecentEvents());
        }
    }

    public static class NeuralNetworkProcessor {
        private final double[][] weights = initializeWeights();
        private final double[] biases = initializeBiases();

        public java.util.List<RecommendationItem> processRecommendations(UserBehaviorProfile profile, 
                RecommendationModel model, RecommendationContext context) {
            
            double[] inputVector = profile.toFeatureVector();
            double[] hiddenLayer = computeHiddenLayer(inputVector);
            double[] outputLayer = computeOutputLayer(hiddenLayer);
            
            return generateItemsFromOutput(outputLayer, context);
        }

        private double[][] initializeWeights() {
            double[][] weights = new double[50][100];
            for (int i = 0; i < weights.length; i++) {
                for (int j = 0; j < weights[i].length; j++) {
                    weights[i][j] = ThreadLocalRandom.current().nextGaussian() * 0.1;
                }
            }
            return weights;
        }

        private double[] initializeBiases() {
            double[] biases = new double[50];
            for (int i = 0; i < biases.length; i++) {
                biases[i] = ThreadLocalRandom.current().nextGaussian() * 0.01;
            }
            return biases;
        }

        private double[] computeHiddenLayer(double[] input) {
            double[] hidden = new double[weights.length];
            for (int i = 0; i < hidden.length; i++) {
                double sum = biases[i];
                for (int j = 0; j < Math.min(input.length, weights[i].length); j++) {
                    sum += input[j] * weights[i][j];
                }
                hidden[i] = sigmoid(sum);
            }
            return hidden;
        }

        private double[] computeOutputLayer(double[] hidden) {
            double[] output = new double[10];
            for (int i = 0; i < output.length; i++) {
                output[i] = hidden[i % hidden.length] * ThreadLocalRandom.current().nextDouble(0.5, 1.5);
            }
            return output;
        }

        private double sigmoid(double x) {
            return 1.0 / (1.0 + Math.exp(-x));
        }

        private java.util.List<RecommendationItem> generateItemsFromOutput(double[] output, RecommendationContext context) {
            return java.util.stream.IntStream.range(0, output.length)
                    .mapToObj(i -> new RecommendationItem(
                            "item_" + i,
                            "category_" + (i % 3),
                            output[i],
                            "AI生成推荐项目 " + i
                    ))
                    .filter(item -> item.getScore() > 0.3)
                    .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                    .limit(5)
                    .collect(Collectors.toList());
        }
    }

    public static class MachineLearningOptimizer {
        private final GradientDescentOptimizer gradientOptimizer = new GradientDescentOptimizer();
        private final GeneticAlgorithmOptimizer geneticOptimizer = new GeneticAlgorithmOptimizer();

        public java.util.List<RecommendationItem> optimizeResults(java.util.List<RecommendationItem> items, 
                UserPreferences preferences) {
            
            items = gradientOptimizer.optimize(items, preferences);
            items = geneticOptimizer.evolve(items, preferences);
            
            return items.stream()
                    .peek(item -> item.setScore(item.getScore() * preferences.getCategoryWeight(item.getCategory())))
                    .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                    .collect(Collectors.toList());
        }
    }

    public static class GradientDescentOptimizer {
        private final double learningRate = 0.01;
        private final int maxIterations = 100;

        public java.util.List<RecommendationItem> optimize(java.util.List<RecommendationItem> items, 
                UserPreferences preferences) {
            
            for (int iteration = 0; iteration < maxIterations; iteration++) {
                double totalLoss = 0.0;
                
                for (RecommendationItem item : items) {
                    double predicted = item.getScore();
                    double target = preferences.getTargetScore(item.getCategory());
                    double loss = Math.pow(predicted - target, 2);
                    totalLoss += loss;
                    
                    double gradient = 2 * (predicted - target);
                    double newScore = predicted - learningRate * gradient;
                    item.setScore(Math.max(0.0, Math.min(1.0, newScore)));
                }
                
                if (totalLoss < 0.001) break;
            }
            
            return items;
        }
    }

    public static class GeneticAlgorithmOptimizer {
        private final int populationSize = 50;
        private final double mutationRate = 0.1;
        private final int generations = 20;

        public java.util.List<RecommendationItem> evolve(java.util.List<RecommendationItem> items, 
                UserPreferences preferences) {
            
            java.util.List<java.util.List<RecommendationItem>> population = initializePopulation(items);
            
            for (int gen = 0; gen < generations; gen++) {
                population = evolveGeneration(population, preferences);
            }
            
            return selectBestIndividual(population, preferences);
        }

        private java.util.List<java.util.List<RecommendationItem>> initializePopulation(
                java.util.List<RecommendationItem> baseItems) {
            
            java.util.List<java.util.List<RecommendationItem>> population = new java.util.ArrayList<>();
            
            for (int i = 0; i < populationSize; i++) {
                java.util.List<RecommendationItem> individual = baseItems.stream()
                        .map(item -> new RecommendationItem(
                                item.getId(),
                                item.getCategory(),
                                item.getScore() * ThreadLocalRandom.current().nextDouble(0.8, 1.2),
                                item.getDescription()
                        ))
                        .collect(Collectors.toList());
                population.add(individual);
            }
            
            return population;
        }

        private java.util.List<java.util.List<RecommendationItem>> evolveGeneration(
                java.util.List<java.util.List<RecommendationItem>> population, UserPreferences preferences) {
            
            java.util.List<java.util.List<RecommendationItem>> newPopulation = new java.util.ArrayList<>();
            
            for (int i = 0; i < populationSize; i++) {
                java.util.List<RecommendationItem> parent1 = selectParent(population, preferences);
                java.util.List<RecommendationItem> parent2 = selectParent(population, preferences);
                java.util.List<RecommendationItem> offspring = crossover(parent1, parent2);
                offspring = mutate(offspring);
                newPopulation.add(offspring);
            }
            
            return newPopulation;
        }

        private java.util.List<RecommendationItem> selectParent(
                java.util.List<java.util.List<RecommendationItem>> population, UserPreferences preferences) {
            
            return population.get(ThreadLocalRandom.current().nextInt(population.size()));
        }

        private java.util.List<RecommendationItem> crossover(java.util.List<RecommendationItem> parent1, 
                java.util.List<RecommendationItem> parent2) {
            
            java.util.List<RecommendationItem> offspring = new java.util.ArrayList<>();
            int crossoverPoint = ThreadLocalRandom.current().nextInt(Math.min(parent1.size(), parent2.size()));
            
            for (int i = 0; i < Math.max(parent1.size(), parent2.size()); i++) {
                if (i < crossoverPoint && i < parent1.size()) {
                    offspring.add(parent1.get(i));
                } else if (i < parent2.size()) {
                    offspring.add(parent2.get(i));
                }
            }
            
            return offspring;
        }

        private java.util.List<RecommendationItem> mutate(java.util.List<RecommendationItem> individual) {
            return individual.stream()
                    .peek(item -> {
                        if (ThreadLocalRandom.current().nextDouble() < mutationRate) {
                            double mutation = ThreadLocalRandom.current().nextGaussian() * 0.1;
                            item.setScore(Math.max(0.0, Math.min(1.0, item.getScore() + mutation)));
                        }
                    })
                    .collect(Collectors.toList());
        }

        private java.util.List<RecommendationItem> selectBestIndividual(
                java.util.List<java.util.List<RecommendationItem>> population, UserPreferences preferences) {
            
            return population.stream()
                    .max((a, b) -> Double.compare(calculateFitness(a, preferences), calculateFitness(b, preferences)))
                    .orElse(population.get(0));
        }

        private double calculateFitness(java.util.List<RecommendationItem> individual, UserPreferences preferences) {
            return individual.stream()
                    .mapToDouble(item -> item.getScore() * preferences.getCategoryWeight(item.getCategory()))
                    .sum();
        }
    }

    @lombok.Data
    public static class UserBehaviorProfile {
        private String userId;
        private java.util.List<BehaviorEvent> events = new java.util.concurrent.CopyOnWriteArrayList<>();
        private UserPreferences preferences = new UserPreferences();
        private double engagementScore = 0.5;
        private double activityLevel = 0.5;
        private LocalDateTime lastUpdated = LocalDateTime.now();

        public UserBehaviorProfile(String userId) {
            this.userId = userId;
        }

        public void addBehaviorEvent(BehaviorEvent event) {
            events.add(event);
            updateMetrics();
            lastUpdated = LocalDateTime.now();
        }

        public void initializeWithDefaults() {
            engagementScore = ThreadLocalRandom.current().nextDouble(0.3, 0.8);
            activityLevel = ThreadLocalRandom.current().nextDouble(0.2, 0.9);
        }

        public int getEventCount() {
            return events.size();
        }

        public java.util.List<BehaviorEvent> getRecentEvents() {
            return events.stream()
                    .filter(event -> event.getTimestamp().isAfter(LocalDateTime.now().minusDays(7)))
                    .collect(Collectors.toList());
        }

        public double[] toFeatureVector() {
            double[] features = new double[20];
            features[0] = engagementScore;
            features[1] = activityLevel;
            features[2] = events.size() / 1000.0;
            
            for (int i = 3; i < features.length; i++) {
                features[i] = ThreadLocalRandom.current().nextDouble();
            }
            
            return features;
        }

        public double getRelevanceWeight(String category) {
            return preferences.getCategoryWeight(category);
        }

        private void updateMetrics() {
            if (events.size() > 10) {
                long recentEvents = events.stream()
                        .filter(event -> event.getTimestamp().isAfter(LocalDateTime.now().minusHours(24)))
                        .count();
                activityLevel = Math.min(1.0, recentEvents / 50.0);
            }
        }
    }

    @lombok.Data
    public static class UserPreferences {
        private ConcurrentHashMap<String, Double> categoryWeights = new ConcurrentHashMap<>();

        public UserPreferences() {
            categoryWeights.put("category_0", ThreadLocalRandom.current().nextDouble(0.5, 1.0));
            categoryWeights.put("category_1", ThreadLocalRandom.current().nextDouble(0.5, 1.0));
            categoryWeights.put("category_2", ThreadLocalRandom.current().nextDouble(0.5, 1.0));
        }

        public double getCategoryWeight(String category) {
            return categoryWeights.getOrDefault(category, 0.5);
        }

        public double getTargetScore(String category) {
            return getCategoryWeight(category) * 0.8;
        }
    }

    @lombok.Data
    public static class RecommendationModel {
        private String modelType;
        private String version = "1.0.0";
        private LocalDateTime trainedAt = LocalDateTime.now();
        private double accuracy = ThreadLocalRandom.current().nextDouble(0.7, 0.95);

        public RecommendationModel(String modelType) {
            this.modelType = modelType;
        }

        public void trainWithProfile(UserBehaviorProfile profile) {
            accuracy = Math.min(0.99, accuracy + ThreadLocalRandom.current().nextDouble(0.01, 0.05));
            trainedAt = LocalDateTime.now();
        }

        public void incrementalTrain(java.util.List<BehaviorEvent> events) {
            accuracy = Math.min(0.99, accuracy + events.size() * 0.001);
            trainedAt = LocalDateTime.now();
        }
    }

    @lombok.Data
    @lombok.AllArgsConstructor
    public static class RecommendationItem {
        private String id;
        private String category;
        private double score;
        private String description;
    }

    @lombok.Data
    @lombok.AllArgsConstructor
    public static class BehaviorEvent {
        private String eventType;
        private String category;
        private LocalDateTime timestamp;
        private java.util.Map<String, Object> properties;
    }

    @lombok.Data
    public static class RecommendationContext {
        private String sessionId;
        private LocalDateTime requestTime = LocalDateTime.now();
        private java.util.Map<String, Object> contextData = new ConcurrentHashMap<>();
    }

    @lombok.Builder
    @lombok.Data
    public static class RecommendationResult {
        private String userId;
        private java.util.List<RecommendationItem> items;
        private double confidence;
        private String modelVersion;
        private long processingTimeNanos;
        private LocalDateTime generatedAt;
    }
}