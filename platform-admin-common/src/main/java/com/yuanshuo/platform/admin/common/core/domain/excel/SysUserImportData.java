package com.yuanshuo.platform.admin.common.core.domain.excel;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import lombok.Data;

/**
 * 用户导入数据对象
 *
 * <AUTHOR>
 */
@Data
public class SysUserImportData {

    @Excel(name = "姓名")
    private String nickName;

    @Excel(name = "账号")
    private String userName;

    @Excel(name = "职务")
    private String roleName;

    @Excel(name = "部门")
    private String deptName;

    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    @Excel(name = "手机")
    private String phonenumber;

    @Excel(name = "企业邮箱")
    private String email;

    @Excel(name = "激活状态")
    private String activeState;

    @Excel(name = "禁用状态")
    private String disabledState;

}
