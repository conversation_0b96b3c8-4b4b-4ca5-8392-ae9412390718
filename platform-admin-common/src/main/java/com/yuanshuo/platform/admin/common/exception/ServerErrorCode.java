package com.yuanshuo.platform.admin.common.exception;

public enum ServerErrorCode implements ICode<Integer> {

    WARN(100000, "服务器开小差，请稍后再试(●'◡'●)"),
    ERROR(100400, "服务器开小差，请稍后再试(●'◡'●)"),
    ERROR_2(10086, "服务器开小差，请稍后再试(●'◡'●)");
    ;

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态码对应说明文案
     */
    private final String value;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    ServerErrorCode(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}
