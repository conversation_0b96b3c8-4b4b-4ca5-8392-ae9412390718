package com.yuanshuo.platform.admin.facade.controller.system.feign;

import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.feign.CustomerFeign;
import com.yuanshuo.platform.admin.api.model.dto.CustomerQueryListDTO;
import com.yuanshuo.platform.admin.api.model.vo.CustomerVO;
import com.yuanshuo.platforma.admin.framework.web.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class CustomerFeignImpl implements CustomerFeign {

    @Autowired
    private CustomerService customerService;

    @Override
    public R<List<CustomerVO>> list(CustomerQueryListDTO customerQueryListDTO) {
        List<CustomerVO> customerVOS = customerService.queryCustomerList(customerQueryListDTO);
        return R.success(customerVOS);
    }
}
