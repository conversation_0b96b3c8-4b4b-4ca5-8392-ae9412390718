package com.yuanshuo.platform.admin.facade.controller.system.feign;

import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.feign.WxSendFeign;
import com.yuanshuo.platform.admin.api.model.dto.WxSendMessageDTO;
import com.yuanshuo.platforma.admin.framework.web.service.WxSendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Slf4j
public class WxSendFeignImpl implements WxSendFeign {

    @Autowired
    private WxSendMessageService wxSendMessageService;

    @Override
    public R<Void> sendTextMessage(WxSendMessageDTO wxSendMessageDTO) {
        wxSendMessageService.sendTextMessage(wxSendMessageDTO.getUserId(),wxSendMessageDTO.getContent());
        return R.success();
    }
}
