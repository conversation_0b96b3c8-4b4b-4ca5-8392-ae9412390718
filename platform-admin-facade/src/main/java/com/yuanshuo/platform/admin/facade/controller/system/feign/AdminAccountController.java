package com.yuanshuo.platform.admin.facade.controller.system.feign;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.common.entity.constants.TokenConstant;
import com.yuanshuo.common.entity.support.LoginUser;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.AccountCreateDTO;
import com.yuanshuo.platform.admin.api.model.dto.AccountImportFailDTO;
import com.yuanshuo.platform.admin.api.model.dto.AccountUpdateDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.AccountQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.AccountVO;
import com.yuanshuo.platform.admin.common.config.RuoYiConfig;
import com.yuanshuo.platform.admin.common.constant.HttpStatus;
import com.yuanshuo.platform.admin.common.core.domain.AjaxResult;
import com.yuanshuo.platform.admin.common.core.page.TableDataInfo;
import com.yuanshuo.platform.admin.common.core.redis.RedisCache;
import com.yuanshuo.platform.admin.common.utils.file.FileUtils;
import com.yuanshuo.platform.admin.common.utils.poi.ExcelUtil;
import com.yuanshuo.platform.admin.domain.service.impl.AccountServiceImpl;
import com.yuanshuo.platforma.admin.framework.log.annotation.OperateLog;
import com.yuanshuo.platforma.admin.framework.log.enums.DataSource;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 账号管理
 */
@Slf4j
@RestController
public class AdminAccountController {

    @Autowired
    private AccountServiceImpl accountService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取账号列表
     */
    @PostMapping("/admin/c/zhgl/list")
    public R<TableDataInfo> list(HttpServletRequest request, @RequestBody AccountQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNo(), queryDTO.getPageSize());
        PageInfo<AccountVO> accountVOPageInfo = accountService.selectAccountList(queryDTO);
        return R.success(getDataTable(accountVOPageInfo));
    }

    /**
     * 新增账号
     */
    @OperateLog(name = OperateLogEnum.Content.Account_Create, type = OperateType.ADD, field = "userId", dataSource = DataSource.AfterServer, operationObjectClass = "accountOperateLogService")
    @PostMapping("/admin/c/zhgl/add")
    public R<String> add(HttpServletRequest request, @Valid @RequestBody AccountCreateDTO createDTO) {
        LoginUser.SysUser user = getLoginUser(request).getUser();
        log.info("adminAdd,token:{},user:{}",  request.getHeader(TokenConstant.userKey),JSON.toJSONString(user));
        createDTO.setCreateBy(user.getNickName());
        String result = accountService.insertAccount(createDTO);
        return R.success(result);
    }

    /**
     * 编辑账号
     */
    @OperateLog(name = OperateLogEnum.Content.Account_Edit, type = OperateType.UPDATE, field = "userId")
    @PostMapping("/admin/c/zhgl/edit")
    public R<String> edit(HttpServletRequest request, @Valid @RequestBody AccountUpdateDTO updateDTO) {
        updateDTO.setUpdateBy(getLoginUser(request).getUserId().toString());
        String result = accountService.updateAccount(updateDTO);
        return R.success(result);
    }

    /**
     * 下载导入模板
     */
    @SneakyThrows
    @PostMapping("/admin/c/zhgl/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        String filePath = RuoYiConfig.getDownloadPath() + "账号导入模板.xlsx";

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        FileUtils.setAttachmentResponseHeader(response, "账号导入模板.xlsx");
        FileUtils.writeBytes(filePath, response.getOutputStream());
    }

    /**
     * 导入账号
     */
    @OperateLog(name = OperateLogEnum.Content.Account_Import, type = OperateType.ADD, dataSource = DataSource.Attribute, field = "userIds")
    @PostMapping("/admin/c/zhgl/import")
    public R<String> importAccount(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        try {
            ExcelUtil<AccountCreateDTO> util = new ExcelUtil<>(AccountCreateDTO.class);
            List<AccountCreateDTO> importList = util.importExcel(file.getInputStream());

            if (importList.isEmpty()) {
                return R.fail("导入数据为空");
            }

            String createBy = getLoginUser(request).getUser().getNickName().toString();
            List<AccountImportFailDTO> failList = accountService.batchImportAccount(importList, createBy);

            // 日志操作对象赋值 开始
            List<String> successNumbers = importList.stream().map(AccountCreateDTO::getPhonenumber).collect(Collectors.toList());
            if (failList != null && failList.size() > 0) {
                List<String> failNumbers = failList.stream().map(AccountImportFailDTO::getPhonenumber).collect(Collectors.toList());
                successNumbers.removeAll(failNumbers);
            }
            if (successNumbers.size() > 0) {
                request.setAttribute("userIds", String.join(",", successNumbers));
            }
            // 日志操作对象赋值 结束

            if (failList.isEmpty()) {
                return R.success("导入成功 "+importList.size() + "/"+importList.size());
            } else {
                // 生成失败数据Excel文件
                ExcelUtil<AccountImportFailDTO> failUtil = new ExcelUtil<>(AccountImportFailDTO.class);
                AjaxResult result = failUtil.exportExcel(failList, "导入失败数据");
                String fileName = (String) result.get("msg");
                R<String> fail = R.fail("导入完成 " + (importList.size()-failList.size() )+ "/"+importList.size());
                fail.setData(fileName);
                return fail;
            }
        } catch (Exception e) {
            return R.fail("导入失败：" + e.getMessage());
        }
    }


    /**
     * 获取登录用户信息
     *
     * @param request
     * @return
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        String userKey = request.getHeader(TokenConstant.userKey);
        if (StrUtil.isBlank(userKey)) {
            return null;
        }
        String obj = (String) redisCache.getCacheObject("login_tokens:" + userKey);
        if (StrUtil.isBlank(obj)) {
            return null;
        }
        return JSON.parseObject(obj, LoginUser.class);
    }


    protected TableDataInfo getDataTable(PageInfo list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list.getList());
        rspData.setTotal(list.getTotal());
        return rspData;
    }
}