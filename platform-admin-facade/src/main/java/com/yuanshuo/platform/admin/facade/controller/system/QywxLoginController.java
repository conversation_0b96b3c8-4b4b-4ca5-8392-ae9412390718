package com.yuanshuo.platform.admin.facade.controller.system;

import com.yuanshuo.platform.admin.common.constant.Constants;
import com.yuanshuo.platform.admin.common.core.domain.AjaxResult;
import com.yuanshuo.platform.admin.common.core.domain.model.QywxLoginBody;
import com.yuanshuo.platform.admin.common.core.domain.model.req.QywxWebLoginUrlReq;
import com.yuanshuo.platforma.admin.framework.web.service.SysLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 微信企业登录
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class QywxLoginController {

    @Autowired
    private SysLoginService loginService;

    /**
     *
     * 第一步 获取企业微信web登录地址
     */
    @PostMapping("/getQywxWebLoginUrl")
    public AjaxResult getQywxWebLoginUrl(@RequestBody @Valid QywxWebLoginUrlReq req) {
        String url = loginService.getQywxWebLoginUrl(req.getEnv());
        return AjaxResult.success("操作成功", url);
    }

    /**
     *
     * 第二步 企业微信授权登录回调
     */
    @GetMapping("/qywxLoginCallback")
    public void qywxLoginCallback(@RequestParam("code") String code,
                                  @RequestParam(value = "state", required = false) String state,
                                  HttpServletResponse response) throws IOException {
        log.info("企业微信授权登录回调 code:{}, state:{}", code, state);
        String url = loginService.getCallbackUrl(state) + code;

        // 请求重定向
        response.sendRedirect(url);
    }

    /**
     *
     * 第三步 企业微信授权登录
     */
    @PostMapping("/qywxLogin")
    public AjaxResult qywxLogin(@RequestBody @Valid QywxLoginBody qywxLoginBody) {
        AjaxResult ajax = AjaxResult.success();
        Object token = loginService.qywxLogin(qywxLoginBody);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }





}
