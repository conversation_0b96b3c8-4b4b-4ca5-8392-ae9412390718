package com.yuanshuo.platform.admin.facade.controller.system.feign;

import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.feign.AdminUserFeign;
import com.yuanshuo.platform.admin.api.model.dto.SysUserDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.SalespersonQueryDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.UserQueryDTO;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.domain.service.ISysUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户管理Feign实现
 */
@RestController
public class AdminUserFeignImpl implements AdminUserFeign {

    @Autowired
    private ISysUserService userService;

    @Override
    public R<List<SysUserDTO>> list(@RequestBody UserQueryDTO request) {
        List<SysUser> users = userService.selectUsersByIdsAndType(request.getIds(), request.getUserType());
        List<SysUserDTO> userDTOs = users.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        return R.success(userDTOs);
    }

    @Override
    public R<List<SysUserDTO>> getSalesperson(SalespersonQueryDTO salespersonQueryDTO) {
        List<SysUser> users = userService.selectNewUserList(salespersonQueryDTO.getUserIdList(), salespersonQueryDTO.getNickNameLike(), salespersonQueryDTO.getIsSalesperson());
        List<SysUserDTO> userDTOs = users.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        return R.success(userDTOs);
    }

    private SysUserDTO convertToDTO(SysUser user) {
        SysUserDTO dto = new SysUserDTO();
        BeanUtils.copyProperties(user, dto);
        return dto;
    }
}