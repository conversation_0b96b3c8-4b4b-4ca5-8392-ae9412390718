package com.yuanshuo.platform.admin.facade.controller.system;


import com.yuanshuo.common.entity.exception.BusinessException;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.*;
import com.yuanshuo.platform.admin.api.model.vo.CustomerDetailVO;
import com.yuanshuo.platform.admin.api.model.vo.CustomerVO;
import com.yuanshuo.platforma.admin.framework.log.annotation.OperateLog;
import com.yuanshuo.platforma.admin.framework.log.enums.DataSource;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateType;
import com.yuanshuo.platforma.admin.framework.web.service.CustomerService;
import com.yuanshuo.platforma.admin.framework.web.service.WxSendMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 客户管理
 */
@RestController
@RequestMapping("/system/customer")
public class CustomerController {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private WxSendMessageService wxSendMessageService;


    // 社会统一信用代码正则表达式（18位数字或大写字母）
    private static final String CREDIT_CODE_REGEX = "^[0-9A-Z]{18}$";
    // 编译正则表达式，提高效率
    private static final Pattern CREDIT_CODE_PATTERN = Pattern.compile(CREDIT_CODE_REGEX);
    /**
     * 新增客户
     */
    @OperateLog(name = OperateLogEnum.Content.Customer_Create, type = OperateType.ADD, field = "customerId", dataSource = DataSource.AfterServer, operationObjectClass = "customerOperateLogService")
    @PostMapping("/save")
    public R<Void> addCustomer(@Valid @RequestBody CustomerSaveDTO customerSaveDTO) {
        // 调用Service层保存客户
        if (customerSaveDTO.getCustomerType() == 1 && !isValid(customerSaveDTO.getUnifiedSocialCreditCode())) {
            throw new BusinessException("请输入社会统一信用代码");
        }
        customerService.saveCustomer(customerSaveDTO);
        return R.success();
    }

    /**
     * 查询客户列表(分页)
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/pageList")
    public R<TableDataInfo<CustomerVO>> pageList(@RequestBody CustomerQueryPageDTO queryDTO) {
        TableDataInfo<CustomerVO> pageResult = customerService.queryCustomerPage(queryDTO);
        return R.success(pageResult);
    }


    /**
     * 查询客户列表(不分页)
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/list")
    public R<List<CustomerVO>> list(@RequestBody CustomerQueryListDTO queryDTO) {
        List<CustomerVO> pageResult = customerService.queryCustomerList(queryDTO);
        return R.success(pageResult);
    }

    /**
     * 获取客户详情
     */
    @PostMapping("/detail")
    public R<CustomerDetailVO> detail(@Valid @RequestBody CustomerDetailDTO customerDetailDTO) {
        CustomerDetailVO detailVO = customerService.detail(customerDetailDTO);
        return R.success(detailVO);
    }

    /**
     * 保存详情页编辑内容
     */
    @OperateLog(name = OperateLogEnum.Content.Customer_Edit, type = OperateType.UPDATE, field = "customerId")
    @PostMapping("/update")
    public R<Void> updateCustomer(@Valid @RequestBody CustomerUpdateDTO dto) {

        // 企业客户校验信用代码
        if (dto.getCustomerType() == 1 && !isValid(dto.getUnifiedSocialCreditCode())) {
            throw new BusinessException("请输入社会统一信用代码");
        }
        customerService.updateCustomer(dto);
        return R.success();
    }


    @PostMapping("/test")
    public R<String> test() {
//        wxSendMessageService.sendTextMessage("***********","这是一条测试文本");
        wxSendMessageService.sendTextMessage(***********L, "这是一条测试文本");
        return R.success("测试成功");
    }

    /**
     * 同步微信用户到数据库
     *
     * @return
     */
    @PostMapping("/syncWechatUsersToDb")
    public R<String> syncWechatUsersToDb() {
        wxSendMessageService.syncWechatUsersToDb();
        return R.success("测试成功");
    }


    /**
     * 验证社会统一信用代码格式
     * @param creditCode 待验证的社会统一信用代码
     * @return 验证通过返回true，否则返回false
     */
    public static boolean isValid(String creditCode) {
        // 先判断是否为null或空字符串
        if (creditCode == null || creditCode.trim().isEmpty()) {
            return false;
        }
        // 使用正则表达式验证格式
        boolean matches = CREDIT_CODE_PATTERN.matcher(creditCode).matches();
        if(!matches){
            throw new BusinessException("社会统一信用代码仅支持数字和大写字母");
        }
        return true;
    }
}
