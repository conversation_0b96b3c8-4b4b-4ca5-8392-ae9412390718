package com.yuanshuo.platform.admin.facade.controller.system.app;

import com.yuanshuo.platform.admin.common.constant.Constants;
import com.yuanshuo.platform.admin.common.core.domain.AjaxResult;
import com.yuanshuo.platform.admin.common.core.domain.model.req.PhoneLoginReq;
import com.yuanshuo.platform.admin.common.core.domain.model.req.SendSmsCodeReq;
import com.yuanshuo.platform.admin.common.core.domain.model.res.PhoneLoginRes;
import com.yuanshuo.platforma.admin.framework.web.service.SysLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 手机号登录
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/app")
public class PhoneLoginController {

    @Autowired
    private SysLoginService loginService;

    /**
     * 发送短信验证码
     */
    @PostMapping("/sendSmsCode")
    public AjaxResult sendSmsCode(@RequestBody @Valid SendSmsCodeReq request) {
        loginService.sendSmsCode(request.getPhone());

        return AjaxResult.success("验证码已发送");
    }

    /**
     * 手机号登录
     *
     * @param request
     * @return
     */
    @PostMapping("/phoneLogin")
    public AjaxResult phoneLogin(HttpServletRequest req, @RequestBody @Valid PhoneLoginReq request) {
        String osType = req.getHeader("os_type");
        request.setOsType(osType);

        // 验证码正确后，调用登录服务生成token（具体参数根据业务逻辑调整）
        PhoneLoginRes phoneLoginRes = loginService.loginByPhone(request);// 假设loginService有此方法
        return AjaxResult.success(phoneLoginRes);
    }

    /**
     * 手机号登录-小元车控
     *
     * @param request
     * @return
     */
    @PostMapping("/phoneLoginForXyck")
    public AjaxResult phoneLoginForXyck(HttpServletRequest req, @RequestBody @Valid PhoneLoginReq request) {
        String osType = req.getHeader("os_type");
        request.setOsType(osType);
        request.setCheckWhiteList(true);
        // 验证码正确后，调用登录服务生成token（具体参数根据业务逻辑调整）
        PhoneLoginRes phoneLoginRes = loginService.loginByPhone(request);// 假设loginService有此方法
        return AjaxResult.success(phoneLoginRes);
    }

    /**
     * 获取手机号白名单
     *
     * @return
     */
    @PostMapping("/getPhoneWhiteList")
    public AjaxResult getPhoneWhiteList() {
        return AjaxResult.success(loginService.getPhoneWhiteList());
    }

}
