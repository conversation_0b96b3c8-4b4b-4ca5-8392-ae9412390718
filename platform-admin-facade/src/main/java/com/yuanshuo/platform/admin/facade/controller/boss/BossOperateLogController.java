package com.yuanshuo.platform.admin.facade.controller.boss;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.platform.admin.common.constant.HttpStatus;
import com.yuanshuo.platform.admin.common.core.controller.BaseController;
import com.yuanshuo.platform.admin.common.core.page.TableDataInfo;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.model.BossOperateLogQueryReq;
import com.yuanshuo.platforma.admin.framework.log.model.OperateLogEnumRes;
import com.yuanshuo.platforma.admin.framework.log.service.OperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;

/**
 * 后台管理-操作日志
 */
@RestController
@RequestMapping("/operateLog")
public class BossOperateLogController extends BaseController {
    @Autowired
    private OperateLogService operateLogService;

    /**
     * 查询操作日志
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BossOperateLogQueryReq queryDTO) {
        PageHelper.startPage(queryDTO.getPageNo(), queryDTO.getPageSize());
        PageInfo pageInfo = operateLogService.pageList(queryDTO);
        return getDataTable(pageInfo);
    }

    /**
     * 获取操作日志枚举
     *
     * @return
     */
    @PostMapping("/operateEnum")
    public OperateLogEnumRes operateEnum() {
        OperateLogEnumRes res = new OperateLogEnumRes();
        res.setItems(new ArrayList<>());
        OperateLogEnum.Module[] typeValues = OperateLogEnum.Module.values();
        for (OperateLogEnum.Module value : typeValues) {
            OperateLogEnumRes.OperateLogEnumItemRes item = new OperateLogEnumRes.OperateLogEnumItemRes();
            item.setCode(value.getCode());
            item.setName(value.getValue());
            item.setType("module");
            res.getItems().add(item);
        }
        OperateLogEnum.Content[] contentValues = OperateLogEnum.Content.values();
        for (OperateLogEnum.Content value : contentValues) {
            OperateLogEnumRes.OperateLogEnumItemRes item = new OperateLogEnumRes.OperateLogEnumItemRes();
            item.setCode(value.getCode());
            item.setName(value.getDescription());
            item.setType("content");
            res.getItems().add(item);
        }
        return res;
    }

    protected TableDataInfo getDataTable(PageInfo list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list.getList());
        rspData.setTotal(list.getTotal());
        return rspData;
    }
}
