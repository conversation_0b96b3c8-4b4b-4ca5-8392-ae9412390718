package com.yuanshuo.platform.admin.facade.controller.system.feign;

import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.platform.admin.api.feign.AdminContactFeign;
import com.yuanshuo.platform.admin.api.model.dto.ContactDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.ContactQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.ContactVO;
import com.yuanshuo.platform.admin.domain.service.impl.ContactService;
import com.yuanshuo.platform.order.api.feign.TradeOrderFeign;
import com.yuanshuo.platform.order.api.model.dto.query.TradeOrderQueryPageDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderAddressVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import com.yuanshuo.common.entity.web.R;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 常用联系人信息
 */
@RestController
public class AdminContactFeignImpl implements AdminContactFeign {

    @Autowired
    private ContactService contactService;
    @Autowired
    TradeOrderFeign tradeOrderFeign;
    /**
     * 获取联系人推荐列表
     */
    @Override
    public R<List<ContactVO>> getRecommendationList(@RequestBody ContactQueryDTO queryDTO) {

        TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
        tradeOrderQueryPageDTO.setPageSize(999);
        tradeOrderQueryPageDTO.setPageNo(1);
        tradeOrderQueryPageDTO.setUserId(queryDTO.getUserId());
        R<TableDataInfo<TradeOrderVO>> tableDataInfoR = tradeOrderFeign.queryPageOrder(tradeOrderQueryPageDTO);

        List<TradeOrderVO> rows = tableDataInfoR.getData().getRows().stream()
                .filter(order -> "completed".equals(order.getStatus()) || "cancelled".equals(order.getStatus()) || "closed".equals(order.getStatus()))
                .collect(Collectors.toList());

        Map<String, Integer> phoneCountMap = new HashMap<>();
        Map<String, Date> phoneLastTimeMap = new HashMap<>();
        Map<String, String> phoneNameMap = new HashMap<>();
        Map<String, Set<String>> locationPhoneMap = new HashMap<>();

        for (TradeOrderVO tradeOrder : rows) {
            countPhones(tradeOrder, phoneCountMap);
            updateLastTime(tradeOrder, phoneLastTimeMap);
            updatePhoneName(tradeOrder, phoneNameMap);
            buildLocationPhoneMap(tradeOrder, locationPhoneMap);

            if (tradeOrder.getChildOrders() != null) {
                for (TradeOrderVO childOrder : tradeOrder.getChildOrders()) {
                    countPhones(childOrder, phoneCountMap);
                    updateLastTime(childOrder, phoneLastTimeMap);
                    updatePhoneName(childOrder, phoneNameMap);
                    buildLocationPhoneMap(childOrder, locationPhoneMap);
                }
            }
        }

        // 当locationId不为空时，只保留对应位置的手机号
        Map<String, Integer> finalPhoneCountMap = phoneCountMap;
        if (StringUtils.hasText(queryDTO.getLocationId())) {
            Set<String> locationPhones = locationPhoneMap.getOrDefault(queryDTO.getLocationId(), new HashSet<>());
            Map<String, Integer> filteredMap = phoneCountMap.entrySet().stream()
                    .filter(entry -> locationPhones.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            if (!filteredMap.isEmpty()) {
                finalPhoneCountMap = filteredMap;
            } else if (queryDTO.getKeyword().length() < 5) {
                return R.success(new ArrayList<>());
            }
        }
        List<String> topPhones = finalPhoneCountMap.entrySet().stream()
                .filter(entry -> queryDTO.getKeyword() == null || entry.getKey().contains(queryDTO.getKeyword()))
                .sorted((e1, e2) -> {
                    int countCompare = e2.getValue().compareTo(e1.getValue());
                    if (countCompare != 0) return countCompare;
                    Date time1 = phoneLastTimeMap.get(e1.getKey());
                    Date time2 = phoneLastTimeMap.get(e2.getKey());
                    if (time1 == null && time2 == null) return 0;
                    if (time1 == null) return 1;
                    if (time2 == null) return -1;
                    return time2.compareTo(time1);
                })
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        List<ContactVO> recommendations = topPhones.stream()
                .map(phone -> {
                    ContactQueryDTO dbQuery = new ContactQueryDTO();
                    dbQuery.setUserId(queryDTO.getUserId());
                    List<ContactVO> existingContacts = contactService.selectContactList(dbQuery);
                    
                    ContactVO existingContact = existingContacts.stream()
                            .filter(c -> phone.equals(c.getPhone()))
                            .findFirst()
                            .orElse(null);
                    
                    if (existingContact != null) {
                        return existingContact;
                    }
                    
                    ContactVO vo = new ContactVO();
                    vo.setPhone(phone);
                    vo.setName(phoneNameMap.getOrDefault(phone, "用户" + phone.substring(phone.length() - 4)));
                    return vo;
                })
                .collect(Collectors.toList());

        return R.success(recommendations);
    }

    private void updateLastTime(TradeOrderVO order, Map<String, Date> phoneLastTimeMap) {
        OrderAddressVO address = order.getOrderAddress();
        if (address != null && address.getCreateTime() != null) {
            if (StringUtils.hasText(address.getSendPhone())) {
                phoneLastTimeMap.merge(address.getSendPhone(), address.getCreateTime(),
                        (existing, current) -> current.after(existing) ? current : existing);
            }
            if (StringUtils.hasText(address.getReceivedPhone())) {
                phoneLastTimeMap.merge(address.getReceivedPhone(), address.getCreateTime(),
                        (existing, current) -> current.after(existing) ? current : existing);
            }
        }
    }
    private void countPhones(TradeOrderVO order, Map<String, Integer> phoneCountMap) {
        OrderAddressVO address = order.getOrderAddress();
        if (address != null) {
            if (StringUtils.hasText(address.getSendPhone())) {
                phoneCountMap.merge(address.getSendPhone(), 1, Integer::sum);
            }
            if (StringUtils.hasText(address.getReceivedPhone())) {
                phoneCountMap.merge(address.getReceivedPhone(), 1, Integer::sum);
            }
        }
    }
    
    private void updatePhoneName(TradeOrderVO order, Map<String, String> phoneNameMap) {
        OrderAddressVO address = order.getOrderAddress();
        if (address != null) {
            if (StringUtils.hasText(address.getSendPhone()) && StringUtils.hasText(address.getSendName())) {
                phoneNameMap.put(address.getSendPhone(), address.getSendName());
            }
            if (StringUtils.hasText(address.getReceivedPhone()) && StringUtils.hasText(address.getReceivedName())) {
                phoneNameMap.put(address.getReceivedPhone(), address.getReceivedName());
            }
        }
    }

    /**
     * 获取联系人列表
     */
    @Override
    public R<List<ContactVO>> list(@RequestBody ContactQueryDTO queryDTO) {
        List<ContactVO> list = contactService.selectContactList(queryDTO);
        return R.success(list);
    }

    /**
     * 根据联系人编号获取详细信息
     */
    @Override
    public R<ContactVO> getById(@RequestBody Long id) {
        ContactVO contactVO = contactService.selectContactById(id);
        return R.success(contactVO);
    }

    /**
     * 新增联系人
     */
    @Override
    public R<Long> add(@Valid @RequestBody ContactDTO contactDTO) {
        Long l = contactService.insertContact(contactDTO);
        return R.success(l);
    }

    /**
     * 修改联系人
     */
    @Override
    public R edit(@Valid @RequestBody ContactDTO contactDTO) {
        contactService.updateContact(contactDTO);
        return R.success();
    }

    /**
     * 删除联系人
     */
    @Override
    public R remove(@RequestBody Long id) {
        contactService.deleteContactById(id);
        return R.success();
    }
    
    private void buildLocationPhoneMap(TradeOrderVO order, Map<String, Set<String>> locationPhoneMap) {
        OrderAddressVO address = order.getOrderAddress();
        if (address != null) {
            if (StringUtils.hasText(address.getSendAssociationCode()) && StringUtils.hasText(address.getSendPhone())) {
                locationPhoneMap.computeIfAbsent(address.getSendAssociationCode(), k -> new HashSet<>()).add(address.getSendPhone());
            }
            if (StringUtils.hasText(address.getReceivedAssociationCode()) && StringUtils.hasText(address.getReceivedPhone())) {
                locationPhoneMap.computeIfAbsent(address.getReceivedAssociationCode(), k -> new HashSet<>()).add(address.getReceivedPhone());
            }
        }
    }
    
}