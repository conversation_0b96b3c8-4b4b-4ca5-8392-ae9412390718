package com.yuanshuo.platform.admin.facade.controller.system;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.SendCancelSmsCodeDTO;
import com.yuanshuo.platform.admin.api.model.dto.SysUserCancelWithSmsDTO;
import com.yuanshuo.platform.admin.api.model.dto.UserCancelCheckDTO;
import com.yuanshuo.platform.admin.api.model.dto.UserBatchCancelCheckDTO;
import com.yuanshuo.platform.admin.api.model.vo.UserCancelCheckVO;
import com.yuanshuo.platform.admin.api.model.dto.SysUserImportDTO;
import com.yuanshuo.platform.admin.api.model.dto.SysUserImportFailDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.SysUserQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.SysUserQueryVO;
import com.yuanshuo.platform.admin.common.config.RuoYiConfig;
import com.yuanshuo.platform.admin.common.constant.HttpStatus;
import com.yuanshuo.platform.admin.common.core.controller.BaseController;
import com.yuanshuo.platform.admin.common.core.domain.AjaxResult;
import com.yuanshuo.platform.admin.common.core.page.TableDataInfo;
import com.yuanshuo.platform.admin.common.exception.BusinessException;
import com.yuanshuo.platform.admin.common.exception.ServerErrorCode;
import com.yuanshuo.platform.admin.common.utils.file.FileUtils;
import com.yuanshuo.platform.admin.common.utils.poi.ExcelUtil;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.service.IContractService;
import com.yuanshuo.platform.admin.domain.service.ISysUserQueryService;
import com.yuanshuo.platform.admin.domain.service.ISysUserService;
import com.yuanshuo.platform.admin.domain.service.impl.SysUserNewRepository;
import com.yuanshuo.platform.order.api.enums.EnumTradeOrderStatus;
import com.yuanshuo.platform.order.api.feign.TradeOrderFeign;
import com.yuanshuo.platform.order.api.model.dto.query.TradeOrderQueryPageDTO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platforma.admin.framework.log.annotation.OperateLog;
import com.yuanshuo.platforma.admin.framework.log.enums.DataSource;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateType;
import com.yuanshuo.platforma.admin.framework.web.service.SysLoginService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 用户管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/userQuery")
public class SysUserQueryController extends BaseController {

    @Autowired
    private ISysUserQueryService sysUserQueryService;

    @Autowired
    private SysUserNewRepository sysUserNewRepository;

    @Autowired
    private IContractService contractService;
    @Autowired
    private TradeOrderFeign tradeOrderFeign;
    @Autowired
    private SysLoginService sysLoginService;

    /**
     * 查询用户列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody SysUserQueryDTO queryDTO) {
        logger.info("SysUserQueryController_list_queryDTO{}", queryDTO);
        PageHelper.startPage(queryDTO.getPageNo(), queryDTO.getPageSize());
        PageInfo pageInfo = sysUserQueryService.selectUserList(queryDTO);
        return getDataTable(pageInfo);
    }

    /**
     * 导出用户列表
     */
    @OperateLog(name = OperateLogEnum.Content.User_Export, type = OperateType.QUERY, dataSource = DataSource.Attribute, field = "userIds")
    @PostMapping("/export")
    public void export(HttpServletRequest request, HttpServletResponse response,
            @RequestBody SysUserQueryDTO queryDTO) {
        queryDTO.setPageSize(9999);
        List<SysUserQueryVO> list = sysUserQueryService.selectUserList(queryDTO).getList();
        ExcelUtil<SysUserQueryVO> util = new ExcelUtil<>(SysUserQueryVO.class);
        util.exportExcel(response, list, "用户查询数据");

        // 日志操作对象数据赋值
        if (list != null && list.size() > 0) {
            request.setAttribute("userIds", String.join(",",
                    list.stream().map(user -> String.valueOf(user.getUserId())).collect(Collectors.toList())));
        }
    }

    /**
     * 用户注销
     */
    @OperateLog(name = OperateLogEnum.Content.User_Logout, type = OperateType.UPDATE, field = "userIds")
    @PostMapping("/cancel")
    public AjaxResult cancelUser(@RequestBody List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return error("用户ID列表不能为空");
        }

        // 检查用户是否存在合同关系
        for (Long userId : userIds) {
            SysUserEntity user = sysUserNewRepository.getById(userId);
            if (user != null && user.getPhonenumber() != null) {
                // 检查该手机号是否为启用状态的合同联系手机号
                if (contractService.isActiveContractContactPhone(user.getPhonenumber())) {
                    return error("您的企业仍存在合同关系，需处理后再申请注销");
                }
            }
        }
        for (Long userId : userIds) {
            TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
            tradeOrderQueryPageDTO.setPageNo(1);
            tradeOrderQueryPageDTO.setPageSize(9999);
            tradeOrderQueryPageDTO.setStatusList(Arrays.asList(EnumTradeOrderStatus.PAYING.getCode(),
                    EnumTradeOrderStatus.INITIALIZE.getCode(), EnumTradeOrderStatus.FULFILLING.getCode()));
            tradeOrderQueryPageDTO.setUserId(userId);
            R<com.yuanshuo.common.entity.support.TableDataInfo<TradeOrderVO>> tableDataInfoR = tradeOrderFeign
                    .queryPageOrder(tradeOrderQueryPageDTO);
            if (tableDataInfoR.getData() != null && tableDataInfoR.getData().getTotal() > 0) {
                return error("订单任务未完成，需完成后再申请注销");
            }
        }
        sysUserNewRepository.lambdaUpdate()
                .in(SysUserEntity::getUserId, userIds)
                .set(SysUserEntity::getAuthStatus, "cancelled")
                .update();
        return success("用户注销成功，共注销" + userIds.size() + "个用户");
    }

    /**
     * 用户批量注销检查
     */
    @PostMapping("/batchCheckCancel")
    public AjaxResult batchCheckUserCancel(@Valid @RequestBody UserBatchCancelCheckDTO request) {
            List<String> contactUsers = new ArrayList<>();
            List<String> orderUsers = new ArrayList<>();

            // 检查每个用户的企业联系人关系
            for (Long userId : request.getUserIds()) {
                SysUserEntity user = sysUserNewRepository.getById(userId);
                if (user != null && user.getPhonenumber() != null) {
                    if (contractService.isActiveContractContactPhone(user.getPhonenumber())) {
                        List<String> customerNames = contractService.getCustomerNameByPhone(user.getPhonenumber());
                        String userName = user.getNickName() != null ? user.getNickName() : user.getUserName();

                        // 如果有多个企业，拼接所有企业名称
                        if (!customerNames.isEmpty()) {
                            String companyNames = String.join("、", customerNames);
                            contactUsers.add(userName + "」是\"" + companyNames + "\"联系人");
                        }
                    }
                }
            }

            // 检查每个用户的订单状态
            for (Long userId : request.getUserIds()) {
                TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
                tradeOrderQueryPageDTO.setPageNo(1);
                tradeOrderQueryPageDTO.setPageSize(1);
                tradeOrderQueryPageDTO.setStatusList(Arrays.asList(EnumTradeOrderStatus.PAYING.getCode(),
                        EnumTradeOrderStatus.INITIALIZE.getCode(), EnumTradeOrderStatus.FULFILLING.getCode()));
                tradeOrderQueryPageDTO.setUserId(userId);
                R<com.yuanshuo.common.entity.support.TableDataInfo<TradeOrderVO>> tableDataInfoR = tradeOrderFeign
                        .queryPageOrder(tradeOrderQueryPageDTO);

                if (tableDataInfoR.getData() != null && tableDataInfoR.getData().getTotal() > 0) {
                    SysUserEntity user = sysUserNewRepository.getById(userId);
                    String userName = user != null
                            ? (user.getNickName() != null ? user.getNickName() : user.getUserName())
                            : "用户" + userId;
                    orderUsers.add(userName);
                }
            }

            // 如果有阻止注销的情况，构建异常文案并返回error
            if (!contactUsers.isEmpty() || !orderUsers.isEmpty()) {
                StringBuilder errorMessage = new StringBuilder();

                if (!contactUsers.isEmpty()) {
                    errorMessage.append("「").append(String.join("；「", contactUsers));

                    if (!orderUsers.isEmpty()) {
                        errorMessage.append("；「").append(String.join("」、「", orderUsers))
                                .append("」仍有订单进行中，无法注销；请等订单完成后，再进行操作。");
                    } else {
                        errorMessage.append("，无法注销；请更换企业联系人信息后，再进行操作。");
                    }
                } else {
                    errorMessage.append("「").append(String.join("」、「", orderUsers))
                            .append("」仍有订单进行中，无法注销；请等订单完成后，再进行操作。");
                }

                throw new BusinessException(ServerErrorCode.ERROR_2,errorMessage.toString());
            }

            return success("检查通过，可以进行注销操作");
    }

    /**
     * 用户注销检查
     */
    @PostMapping("/checkCancel")
    public AjaxResult checkUserCancel(@Valid @RequestBody UserCancelCheckDTO request) {
        try {
            // 验证用户是否存在
            SysUserEntity user = sysUserNewRepository.getById(request.getUserId());
            if (user == null) {
                return error("用户不存在");
            }

            UserCancelCheckVO checkResult = new UserCancelCheckVO();
            checkResult.setUserId(request.getUserId());
            checkResult.setPhoneNumber(user.getPhonenumber());
            checkResult.setCanCancel(true);
            checkResult.setHasContract(false);
            checkResult.setHasUnfinishedOrder(false);
            checkResult.setUnfinishedOrderCount(0L);

            // 检查用户是否存在合同关系
            if (user.getPhonenumber() != null) {
                if (contractService.isActiveContractContactPhone(user.getPhonenumber())) {
                    checkResult.setHasContract(true);
                    checkResult.setCanCancel(false);
                    List<String> customerNames = contractService.getCustomerNameByPhone(user.getPhonenumber());
                    String customerName = customerNames.isEmpty() ? "" : String.join("、", customerNames);
                    checkResult.setCustomerName(customerName);
                    checkResult.setContractMessage("存在启用状态的合同关系");
                    checkResult.setBlockReason("您的企业仍存在合同关系，需处理后再申请注销");
                }
            }

            // 检查用户是否有未完成的订单
            if (checkResult.getCanCancel()) {
                TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
                tradeOrderQueryPageDTO.setPageNo(1);
                tradeOrderQueryPageDTO.setPageSize(9999);
                tradeOrderQueryPageDTO.setStatusList(Arrays.asList(EnumTradeOrderStatus.PAYING.getCode(),
                        EnumTradeOrderStatus.INITIALIZE.getCode(), EnumTradeOrderStatus.FULFILLING.getCode()));
                tradeOrderQueryPageDTO.setUserId(request.getUserId());
                R<com.yuanshuo.common.entity.support.TableDataInfo<TradeOrderVO>> tableDataInfoR = tradeOrderFeign
                        .queryPageOrder(tradeOrderQueryPageDTO);

                if (tableDataInfoR.getData() != null && tableDataInfoR.getData().getTotal() > 0) {
                    checkResult.setHasUnfinishedOrder(true);
                    checkResult.setCanCancel(false);
                    checkResult.setUnfinishedOrderCount(tableDataInfoR.getData().getTotal());
                    checkResult.setOrderMessage("存在未完成的订单任务");
                    checkResult.setBlockReason("订单任务未完成，需完成后再申请注销");
                }
            }

            return success(checkResult);
        } catch (Exception e) {
            return error("检查失败：" + e.getMessage());
        }
    }

    /**
     * 发送注销验证码
     */
    @PostMapping("/sendCancelSmsCode")
    public AjaxResult sendCancelSmsCode(@Valid @RequestBody SendCancelSmsCodeDTO request) {
        try {
            // 验证用户是否存在
            SysUserEntity user = sysUserNewRepository.getById(request.getUserId());
            if (user == null) {
                return error("用户不存在");
            }

            // 发送验证码
            sysLoginService.sendCancelSmsCode(user.getPhonenumber());
            return success("验证码发送成功");
        } catch (Exception e) {
            return error("验证码发送失败：" + e.getMessage());
        }
    }

    /**
     * 用户注销（带短信验证码）
     * 注意：建议在调用此接口前先调用 /checkCancel 接口进行检查
     */
    @OperateLog(name = OperateLogEnum.Content.User_Logout, type = OperateType.UPDATE, field = "userIds")
    @PostMapping("/cancelWithSms")
    public AjaxResult cancelUserWithSms(@Valid @RequestBody SysUserCancelWithSmsDTO request) {
        // 验证用户是否存在
        SysUserEntity user = sysUserNewRepository.getById(request.getUserId());
        if (user == null) {
            return error("用户不存在");
        }

        // 验证短信验证码
        try {
            sysLoginService.validateCancelSmsCode(user.getPhonenumber(), request.getSmsCode());
        } catch (Exception e) {
            return error(e.getMessage());
        }

        // 检查用户是否存在合同关系
        if (user.getPhonenumber() != null) {
            // 检查该手机号是否为启用状态的合同联系手机号
            if (contractService.isActiveContractContactPhone(user.getPhonenumber())) {
                return error("您的企业仍存在合同关系，需处理后再申请注销");
            }
        }

        // 检查用户是否有未完成的订单
        TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
        tradeOrderQueryPageDTO.setPageNo(1);
        tradeOrderQueryPageDTO.setPageSize(9999);
        tradeOrderQueryPageDTO.setStatusList(Arrays.asList(EnumTradeOrderStatus.PAYING.getCode(),
                EnumTradeOrderStatus.INITIALIZE.getCode(), EnumTradeOrderStatus.FULFILLING.getCode()));
        tradeOrderQueryPageDTO.setUserId(request.getUserId());
        R<com.yuanshuo.common.entity.support.TableDataInfo<TradeOrderVO>> tableDataInfoR = tradeOrderFeign
                .queryPageOrder(tradeOrderQueryPageDTO);
        if (tableDataInfoR.getData() != null && tableDataInfoR.getData().getTotal() > 0) {
            return error("订单任务未完成，需完成后再申请注销");
        }

        // 执行注销操作
        sysUserNewRepository.lambdaUpdate()
                .eq(SysUserEntity::getUserId, request.getUserId())
                .set(SysUserEntity::getAuthStatus, "cancelled")
                .update();
        return success("用户注销成功");
    }

    /**
     * 设置用户为白名单
     */
    @PostMapping("/setWhitelist")
    public AjaxResult setUserWhitelist(@RequestBody Long userId) {
        boolean result = sysUserQueryService.setUserWhitelist(userId);
        if (result) {
            return success("设置白名单成功");
        } else {
            return error("设置白名单失败");
        }
    }

    /**
     * 移除用户白名单
     */
    @PostMapping("/removeWhitelist")
    public AjaxResult removeUserWhitelist(@RequestBody Long userId) {
        boolean result = sysUserQueryService.removeUserWhitelist(userId);
        if (result) {
            return success("移除白名单成功");
        } else {
            return error("移除白名单失败");
        }
    }

    /**
     * 下载用户导入模板
     */
    @SneakyThrows
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        String filePath = RuoYiConfig.getDownloadPath() + "用户导入模板.xlsx";

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        FileUtils.setAttachmentResponseHeader(response, "用户导入模板.xlsx");
        FileUtils.writeBytes(filePath, response.getOutputStream());
    }

    /**
     * 导入用户
     */
    @OperateLog(name = OperateLogEnum.Content.User_Import, type = OperateType.ADD, dataSource = DataSource.Attribute, field = "userIds")
    @PostMapping("/import")
    public AjaxResult importUser(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        try {
            ExcelUtil<SysUserImportDTO> util = new ExcelUtil<>(SysUserImportDTO.class);
            List<SysUserImportDTO> importList = util.importExcel(file.getInputStream());

            if (importList.isEmpty()) {
                return error("导入数据为空");
            }

            String createBy = getLoginUser().getUserId().toString();
            List<SysUserImportFailDTO> failList = sysUserQueryService.batchImportUser(importList, createBy);

            // 日志操作对象赋值 开始
            List<String> successNumbers = importList.stream().map(SysUserImportDTO::getPhonenumber)
                    .collect(Collectors.toList());
            if (failList != null && failList.size() > 0) {
                List<String> failNumbers = failList.stream().map(SysUserImportFailDTO::getPhonenumber)
                        .collect(Collectors.toList());
                successNumbers.removeAll(failNumbers);
            }
            if (successNumbers.size() > 0) {
                request.setAttribute("userIds", String.join(",", successNumbers));
            }
            // 日志操作对象赋值 结束

            if (failList.isEmpty()) {
                return success("导入成功 " + importList.size() + "/" + importList.size());
            } else {
                // 生成失败数据Excel文件
                ExcelUtil<SysUserImportFailDTO> failUtil = new ExcelUtil<>(SysUserImportFailDTO.class);
                AjaxResult result = failUtil.exportExcel(failList, "导入失败数据");
                String fileName = (String) result.get("msg");
                AjaxResult fail = error("导入完成 " + (importList.size() - failList.size()) + "/" + importList.size());
                fail.put("data", fileName);
                return fail;
            }
        } catch (Exception e) {
            return error("导入失败：" + e.getMessage());
        }
    }

    protected TableDataInfo getDataTable(PageInfo list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list.getList());
        rspData.setTotal(list.getTotal());
        return rspData;
    }
}