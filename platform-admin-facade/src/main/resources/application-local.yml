server:
  port: 9201
spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless.yszx.ink:8848
        namespace:  dev
        group: local-zzw
      config:
        server-addr:  nacos-headless.yszx.ink:8848
        file-extension: yml
        namespace: dev
logging:
  config: classpath:custom-logback.xml

# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 4.8.0
  # 版权年份
  copyrightYear: 2025
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /Users/<USER>/Downloads
  # 获取ip地址开关
  addressEnabled: false
  captchaType: math