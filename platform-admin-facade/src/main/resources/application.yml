jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:K1mSrytOmprauj7N0wml3eagBp3jGj3P}
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: platform-admin
  cloud:
    nacos:
      username: nacos
      password: nacos123
      discovery:
        server-addr: ${NACOS_SERVER_ADDR}
        namespace: ${NACOS_NAMESPACE}
      config:
        namespace: ${spring.cloud.nacos.discovery.namespace}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
  config:
    import:
      - optional:application-${spring.profiles.active}.yml
      - optional:nacos:${spring.application.name}.${spring.cloud.nacos.config.file-extension}
      - optional:nacos:share-config.${spring.cloud.nacos.config.file-extension}
      - optional:nacos:datasource-mysql.${spring.cloud.nacos.config.file-extension}
      - optional:nacos:datasource-redis.${spring.cloud.nacos.config.file-extension}
#微信授权
wx:
  miniapp:
    configs:
      - appid: wxb65b7b4d3e8385b3
        secret: bbe9d01ae22825afe048926708d008d3
        msgDataFormat: JSON
      - appid: wx7afbdb118cbe0a87
        secret: d1cb23dde75cecda629376c2d8f2a2e2
        msgDataFormat: JSON
      - appid: wxbec233577ce5f7ce
        secret: 67bd0618ffba63b4e5dff7310a83ecf5
        msgDataFormat: JSON
phone:
  checkWhiteList: 13221058679,12341123123
# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 4.8.0
  # 版权年份
  copyrightYear: 2025
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /Users/<USER>/Downloads
  # 获取ip地址开关
  addressEnabled: false
  captchaType: math