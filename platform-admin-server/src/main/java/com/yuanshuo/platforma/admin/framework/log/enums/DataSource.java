package com.yuanshuo.platforma.admin.framework.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 填充数据来源
 */
@Getter
@AllArgsConstructor
public enum DataSource {
    Front("front", "前端"),
    BeforeServer("beforeServer", "服务前查询"),
    AfterServer("afterServer", "服务后查询"),
    Return("return", "返回数据中查询"),
    Attribute("attribute", "Attribute中查询"),

    ;
    private final String type;
    private final String value;

}
