package com.yuanshuo.platforma.admin.framework.security.handler;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import com.alibaba.fastjson2.JSON;
import com.yuanshuo.platform.admin.common.constant.Constants;
import com.yuanshuo.platform.admin.common.core.domain.AjaxResult;
import com.yuanshuo.platform.admin.common.core.domain.model.LoginUser;
import com.yuanshuo.platform.admin.common.utils.MessageUtils;
import com.yuanshuo.platform.admin.common.utils.ServletUtils;
import com.yuanshuo.platform.admin.common.utils.StringUtils;
import com.yuanshuo.platforma.admin.framework.manager.AsyncManager;
import com.yuanshuo.platforma.admin.framework.manager.factory.AsyncFactory;
import com.yuanshuo.platforma.admin.framework.web.service.TokenService;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
public class UserLogoutSuccessHandler implements LogoutSuccessHandler {

    @Autowired
    private TokenService tokenService;

    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (null != loginUser) {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, MessageUtils.message("user.logout.success")));
        }
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.success(MessageUtils.message("user.logout.success"))));
    }
}
