package com.yuanshuo.platforma.admin.framework.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型
 */
@Getter
@AllArgsConstructor
public enum OperateType {
    ADD("add", "新增"),
    DELETE("delete", "删除"),
    UPDATE("update", "修改"),
    QUERY("query", "查询"),
    ;
    private final String type;
    private final String value;

    public static OperateType getByCode(String code) {
        for (OperateType type : OperateType.values()) {
            if (type.getType().equals(code))
                return type;
        }
        return null;
    }
}
