package com.yuanshuo.platforma.admin.framework.log.model;

import com.yuanshuo.common.entity.support.PageQuery;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台操作日志查询
 */
@Data
public class BossOperateLogQueryReq extends PageQuery {
    /**
     * 创建开始时间
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建结束时间
     */
    private LocalDateTime createTimeEnd;

    /**
     * 业务模块
     */
    private String module;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人员(模糊匹配 名称、id)
     */
    private String operateUser;
}