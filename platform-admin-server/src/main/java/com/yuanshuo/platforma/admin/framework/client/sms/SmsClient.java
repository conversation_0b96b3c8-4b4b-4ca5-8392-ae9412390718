package com.yuanshuo.platforma.admin.framework.client.sms;

import com.yuanshuo.platforma.admin.framework.client.dto.MessageDTO;
import com.yuanshuo.platforma.admin.framework.client.dto.MessageSendDTO;
import com.yuanshuo.platforma.admin.framework.config.SmsProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class SmsClient {

    @Resource
    private SmsFeign messasgeSendFeign;

    @Resource
    private SmsProperties smsProperties;

    public void send(String phone, String code) {

        if (!smsProperties.isEnabled()) {
            log.info("短信服务未启用，不发送短信");
            return;
        }

        MessageSendDTO messageSendDTO = new MessageSendDTO();
        messageSendDTO.setTemplateCode(smsProperties.getTemplateCode());
        messageSendDTO.setAppId(smsProperties.getAppId());
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setReceivers(phone);
        Map<String, String> variables = new HashMap<>();
        variables.put("code", code);
        messageDTO.setVariables(variables);
        messageSendDTO.setMessageDTO(messageDTO);

        messasgeSendFeign.singleSend(messageSendDTO);
    }
}
