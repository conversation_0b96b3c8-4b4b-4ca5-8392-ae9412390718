package com.yuanshuo.platforma.admin.framework.log.service;


import com.yuanshuo.common.entity.support.LoginUser;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;

import java.util.Map;

/**
 * 操作日志服务
 */
public interface OperateLogBaseInterface {
    /**
     * 获取操作对象
     *
     * @param content   操作内容
     * @param reqPar    传入参数
     * @param loginUser 用户参数
     * @return 目前只支持返回Map和List<Map>2种格式，返回数据中必须包含操作对象字段名称
     */
    abstract Object getOperateObject(OperateLogEnum.Content content, Map<String, Object> reqPar, LoginUser loginUser);


}
