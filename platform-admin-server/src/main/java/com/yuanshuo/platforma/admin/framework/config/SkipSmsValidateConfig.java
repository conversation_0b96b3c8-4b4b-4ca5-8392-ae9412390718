package com.yuanshuo.platforma.admin.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 跳过短信验证码的配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "login.skip-sms-validate")
public class SkipSmsValidateConfig {
    private boolean enabled;
    private List<PhoneCodePair> users;

    @Data
    public static class PhoneCodePair {
        private String phone;
        private String code;

    }


}

