package com.yuanshuo.platforma.admin.framework.security.auth.password;

import com.yuanshuo.platform.admin.domain.service.ISysUserAuthService;
import com.yuanshuo.platforma.admin.framework.web.service.SysPasswordService;
import com.yuanshuo.platforma.admin.framework.web.service.SysPermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.core.domain.model.LoginUser;
import com.yuanshuo.platform.admin.common.enums.UserStatus;
import com.yuanshuo.platform.admin.common.exception.ServiceException;
import com.yuanshuo.platform.admin.common.utils.MessageUtils;
import com.yuanshuo.platform.admin.common.utils.StringUtils;
import com.yuanshuo.platform.admin.domain.service.ISysUserService;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {


    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysUserAuthService iSysUserAuthService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService permissionService;


    /**
     * 账号密码登录
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser user = userService.selectUserByUserName(username);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }
        passwordService.validate(user);
        return createLoginUser(user);
    }


    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
    }
}
