package com.yuanshuo.platforma.admin.framework.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageSendDTO {

    /**
     * 模版CODE
     */
    private String templateCode;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 消息相关的参数
     * 当业务类型为"send"，必传
     */
    private MessageDTO messageDTO;
}
