package com.yuanshuo.platforma.admin.framework.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class OperateLogEnum {

    /**
     * 日志模块
     */
    @Getter
    @AllArgsConstructor
    public enum Module {
        CityConfig("CityConfig", "城市配置"),
        ElectricFence("ElectricFence", "电子围栏"),
        Location("Location", "点位管理"),
        Route("Route", "路线管理"),
        Supplier("Supplier", "运力商管理"),
        Vehicle("Vehicle", "车辆管理"),
        Carrier("Carrier", "运力组"),
        ReservationConfig("ReservationConfig", "预约配置"),
        AddServiceConfig("AddServiceConfig", "增值服务配置"),
        PricingConfig("PricingConfig", "计价配置"),
        DispatchConfig("DispatchConfig", "计价配置"),
        User("User", "用户管理"),
        Customer("Customer", "客户管理"),
        Account("Account", "账号管理"),
        Contract("Contract", "合同管理"),
        Order("Order", "订单管理"),
        Settlement("Settlement", "结算管理"),
        Marketing("Marketing", "营销管理"),
        Personnel("Personnel", "员工管理"),
        ;
        private final String code;
        private final String value;

        public static Module getByCode(String code) {
            for (Module type : Module.values()) {
                if (type.getCode().equals(code))
                    return type;
            }
            return null;
        }
    }


    /**
     * 操作内容
     */
    @Getter
    @AllArgsConstructor
    public enum Content {
        City_Create("City_Create", "创建城市", Module.CityConfig),
        City_Edit("City_Edit", "编辑城市", Module.CityConfig),
        City_Delete("City_Delete", "删除城市", Module.CityConfig),
        ElectricFence_Create("ElectricFence_Create", "创建电子围栏", Module.ElectricFence),
        ElectricFence_Edit("ElectricFence_Edit", "编辑电子围栏", Module.ElectricFence),
        ElectricFence_Delete("ElectricFence_Delete", "删除电子围栏", Module.ElectricFence),
        Location_Create("Location_Create", "新增点位", Module.Location),
        Location_Edit("Location_Edit", "编辑点位", Module.Location),
        Location_Export("Location_Export", "导出点位", Module.Location),
        Location_Sync("Location_Sync", "同步点位", Module.Location),
        Route_Create("Route_Create", "新增路线", Module.Route),
        Route_Edit("Route_Edit", "编辑路线", Module.Route),
        Route_Export("Route_Export", "导出路线", Module.Route),
        Route_Sync("Route_Sync", "同步路线", Module.Route),
        Supplier_Create("Supplier_Create", "新增运力商", Module.Supplier),
        Supplier_Edit("Supplier_Edit", "编辑运力商", Module.Supplier),
        Supplier_Delete("Supplier_Delete", "删除运力商", Module.Supplier),
        Vehicle_Create("Vehicle_Create", "新增车辆", Module.Vehicle),
        Vehicle_Edit("Vehicle_Edit", "编辑车辆", Module.Vehicle),
        Vehicle_Delete("Vehicle_Delete", "删除车辆", Module.Vehicle),
        Vehicle_Export("Vehicle_Export", "导出车辆", Module.Vehicle),
        Vehicle_Sync("Vehicle_Sync", "同步车辆", Module.Vehicle),
        Carrier_Create("Carrier_Create", "新增运力组", Module.Carrier),
        Carrier_Edit("Carrier_Edit", "编辑运力组", Module.Carrier),
        Carrier_Delete("Carrier_Delete", "删除运力组", Module.Carrier),
        ReservationConfig_Create("ReservationConfig_Create", "新增预约规则", Module.ReservationConfig),
        ReservationConfig_Edit("ReservationConfig_Edit", "编辑预约规则", Module.ReservationConfig),
        ReservationConfig_Delete("ReservationConfig_Delete", "删除预约规则", Module.ReservationConfig),
        AddServiceConfig_Create("AddServiceConfig_Create", "新增增值服务规则", Module.AddServiceConfig),
        AddServiceConfig_Edit("AddServiceConfig_Edit", "编辑增值服务规则", Module.AddServiceConfig),
        AddServiceConfig_Delete("AddServiceConfig_Delete", "删除增值服务规则", Module.AddServiceConfig),
        PricingConfig_Create("PricingConfig_Create", "新增计价规则", Module.PricingConfig),
        PricingConfig_Edit("PricingConfig_Edit", "编辑计价规则", Module.PricingConfig),
        PricingConfig_Delete("PricingConfig_Delete", "删除计价规则", Module.PricingConfig),
        PricingConfig_Start("PricingConfig_Start", "启用计价规则", Module.PricingConfig),
        PricingConfig_Stop("PricingConfig_Stop", "停用计价规则", Module.PricingConfig),
        DispatchConfig_Create("DispatchConfig_Create", "新增派单规则", Module.DispatchConfig),
        DispatchConfig_Edit("DispatchConfig_Edit", "编辑派单规则", Module.DispatchConfig),
        DispatchConfig_Delete("DispatchConfig_Delete", "删除派单规则", Module.DispatchConfig),
        User_Logout("User_Logout", "注销用户", Module.User),
        User_Import("User_Import", "导入用户", Module.User),
        User_Export("User_Export", "导出用户", Module.User),
        Customer_Create("Customer_Create", "创建客户", Module.Customer),
        Customer_Edit("Customer_Edit", "编辑客户", Module.Customer),
        Account_Create("Account_Create", "创建账号", Module.Account),
        Account_Edit("Account_Edit", "编辑账号", Module.Account),
        Account_Import("Account_Import", "导入账号", Module.Account),
        Contract_Create("Contract_Create", "创建合同", Module.Contract),
        Contract_Edit("Contract_Edit", "编辑合同", Module.Contract),
        Contract_Pass("Contract_Pass", "通过合同", Module.Contract),
        Contract_Cancel("Contract_Cancel", "作废合同", Module.Contract),
        Order_Cancel("Order_Cancel", "取消订单", Module.Order),
        Order_Refund("Order_Refund", "订单退款", Module.Order),
        Order_FulfillmentFail("Order_FulfillmentFail", "履约失败", Module.Order),
        Settlement_Edit("Settlement_Edit", "编辑结账单", Module.Settlement),
        Settlement_Create("Settlement_Create", "结账", Module.Settlement),
        Marketing_Create("Marketing_Create", "新建活动", Module.Marketing),
        Marketing_Edit("Marketing_Edit", "编辑活动", Module.Marketing),
        Marketing_Pass("Marketing_Pass", "通过活动", Module.Marketing),
        Marketing_Cancel("Marketing_Cancel", "作废活动", Module.Marketing),
        Personnel_Create("Personnel_Create", "新建账号", Module.Personnel),
        Personnel_Eidt("Personnel_Eidt", "编辑账号", Module.Personnel),
        Personnel_Delete("Personnel_Delete", "删除账号", Module.Personnel),
        Personnel_Import("Personnel_Import", "导入账号", Module.Personnel),
        ;
        private final String code;
        private final String description;
        private final Module module;

        public static Content getByCode(String code) {
            for (Content type : Content.values()) {
                if (type.getCode().equals(code))
                    return type;
            }
            return null;
        }
    }
}
