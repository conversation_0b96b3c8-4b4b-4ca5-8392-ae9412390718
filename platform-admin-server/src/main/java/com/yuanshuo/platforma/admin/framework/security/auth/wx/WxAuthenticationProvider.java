package com.yuanshuo.platforma.admin.framework.security.auth.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.hutool.core.util.ObjectUtil;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.core.domain.model.LoginUser;
import com.yuanshuo.platform.admin.common.core.domain.model.WxLoginBody;
import com.yuanshuo.platform.admin.api.enums.Platform;
import com.yuanshuo.platform.admin.common.enums.UserStatus;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.common.exception.ServerErrorCode;
import com.yuanshuo.platform.admin.common.exception.ServiceException;
import com.yuanshuo.platform.admin.common.utils.SecurityUtils;
import com.yuanshuo.platform.admin.common.utils.StringUtils;
import com.yuanshuo.platform.admin.common.utils.TransferUtils;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.service.ISysUserAuthService;
import com.yuanshuo.platform.admin.domain.service.impl.SysUserNewRepository;
import com.yuanshuo.platforma.admin.framework.web.service.SysPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component
public class WxAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private ISysUserAuthService sysUserAuthService;

    @Autowired
    private SysUserNewRepository sysUserService;

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private SysPermissionService permissionService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        WxLoginBody wxLoginBody = (WxLoginBody) authentication.getPrincipal();
        UserDetails userDetails = null;
        try {
            userDetails = loadUserByOpenId(wxLoginBody);
        } catch (Exception e) {
            log.error("微信小程序授权登录失败", e);
            throw ServerErrorCode.ERROR.exp("微信小程序授权登录失败");
        }
        return new WxAuthenticationToken(userDetails, userDetails.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return WxAuthenticationToken.class.isAssignableFrom(authentication);
    }

    private UserDetails loadUserByOpenId(WxLoginBody wxLoginBody) throws Exception {
        if (wxLoginBody.getAppId() != null) {
            if (!wxMaService.switchover(wxLoginBody.getAppId())) {
                throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", wxLoginBody.getAppId()));
            }
        }
        WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(wxLoginBody.getCode());
        if (session == null) {
            throw ServerErrorCode.WARN.exp("微信小程序未绑定系统账号");
        }
        // 获取微信用户手机号
        WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxMaService.getUserService().getPhoneNumber(wxLoginBody.getPhoneCode());
        if (ObjectUtil.isEmpty(wxMaPhoneNumberInfo)) {
            log.info("微信获取手机号失败:{}", wxLoginBody);
            throw ServerErrorCode.WARN.exp("微信授权失败");
        }
        SysUserEntity existingUser;
        String userType ;
        String code;
        // 根据手机号和用户类型查找重复用户
        if (wxLoginBody.getAppId().equals("wxb65b7b4d3e8385b3")) {
            userType = UserType.XSXGJ.getCode();
            code=   Platform.OTHER.getCode();
        } else {
            userType = UserType.C.getCode();
            code =  Platform.XYCK_MINI_PROGRAM.getCode();
        }
        existingUser = sysUserService.selectUserByPhoneAndUserType(wxMaPhoneNumberInfo.getPhoneNumber(), userType);

        SysUserEntity sysUser = null;
        
        if (existingUser != null) {
            // 重复用户，检查auth_status
            if ("inactive".equals(existingUser.getAuthStatus())) {
                // 未激活状态，修改为未实名
                existingUser.setAuthStatus("unverified");
                existingUser.setUpdateTime(LocalDateTime.now());
                sysUserService.updateById(existingUser);
            }
            sysUser = existingUser;
        } else {
            // 解密用户信息
            WxMaUserInfo wxMaUserInfo = wxMaService.getUserService().getUserInfo(session.getSessionKey(),
                    wxLoginBody.getEncryptedData(), wxLoginBody.getIv());
            // 创建新用户
            sysUser = new SysUserEntity();
            sysUser.setNickName("元朔用户" + StringUtils.right(wxMaPhoneNumberInfo.getPhoneNumber(), 4));
            sysUser.setSex(wxMaUserInfo.getGender());
            sysUser.setUserName(session.getOpenid());
            sysUser.setPassword(SecurityUtils.encryptPassword(session.getOpenid()));
            sysUser.setPhonenumber(wxMaPhoneNumberInfo.getPhoneNumber());
            sysUser.setUserType(userType);
            sysUser.setPlatform(code);
            sysUser.setCreateTime(LocalDateTime.now());
            sysUser.setUpdateTime(LocalDateTime.now());
            sysUser.setAuthStatus("unverified");
            sysUserService.save(sysUser);
        }
        if (UserStatus.DELETED.getCode().equals(sysUser.getDelFlag())) {
            throw ServerErrorCode.WARN.exp("用户已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("账号已停用");
        }
        if ("cancelled".equals(sysUser.getAuthStatus())) {
            throw new ServiceException("账号已注销");
        }

        return createLoginUser(TransferUtils.transfer(sysUser, SysUser::new, (from, to) -> {
            to.setOpenId(session.getOpenid());
        }));
    }


    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
    }
}
