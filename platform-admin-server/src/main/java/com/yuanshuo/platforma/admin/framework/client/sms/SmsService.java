package com.yuanshuo.platforma.admin.framework.client.sms;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Configuration
@EnableAsync
@Component
public class SmsService {

    @Resource
    private SmsClient smsClient;

    @Async
    public void send(String phone,  String code) {
        smsClient.send(phone,  code);
    }

}
