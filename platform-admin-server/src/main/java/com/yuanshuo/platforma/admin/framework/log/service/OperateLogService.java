package com.yuanshuo.platforma.admin.framework.log.service;

import com.github.pagehelper.PageInfo;
import com.yuanshuo.platform.admin.common.utils.TransferUtils;
import com.yuanshuo.platform.admin.domain.entity.OperateLogEntity;
import com.yuanshuo.platform.admin.domain.entity.dto.OperateLogQueryDTO;
import com.yuanshuo.platform.admin.domain.service.impl.OperateLogRepository;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateType;
import com.yuanshuo.platforma.admin.framework.log.model.BossOperateLogQueryReq;
import com.yuanshuo.platforma.admin.framework.log.model.BossOperateLogRes;
import com.yuanshuo.platforma.admin.framework.log.model.OperateLogReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class OperateLogService {


    @Autowired
    private OperateLogRepository operateLogRepository;

    /**
     * 保存日志
     *
     * @param saveReq
     * @return
     */
    public void saveOperateLog(OperateLogReq saveReq) {
        OperateLogEntity operateLogEntity = TransferUtils.transfer(saveReq, OperateLogEntity::new);
        if (!operateLogRepository.save(operateLogEntity)) {
            log.error("保存操作日志数据失败,{}", saveReq);
        }
    }

    /**
     * 查询日志列表
     *
     * @param queryReq
     * @return
     */
    public PageInfo<BossOperateLogRes> pageList(BossOperateLogQueryReq queryReq) {
        OperateLogQueryDTO queryDTO = TransferUtils.transfer(queryReq, OperateLogQueryDTO::new);
        PageInfo<OperateLogEntity> entityPage = operateLogRepository.pageList(queryDTO);
        PageInfo<BossOperateLogRes> result = TransferUtils.transfer(entityPage, PageInfo<BossOperateLogRes>::new);
        if (result.getList() != null && !result.getList().isEmpty()) {
            result.setList(TransferUtils.transfers(entityPage.getList(), BossOperateLogRes::new, (s, d) -> {
                OperateLogEnum.Content content = OperateLogEnum.Content.getByCode(s.getOperationContent());
                OperateLogEnum.Module module = OperateLogEnum.Module.getByCode(s.getOperateModule());
                OperateType type = OperateType.getByCode(s.getOperateType());
                d.setOperationContent(content != null ? content.getDescription() : ("未知操作:" + s.getOperationContent()));
                d.setOperateModule(module != null ? module.getValue() : ("未知模块:" + s.getOperateModule()));
                d.setOperateType(type != null ? type.getValue() : ("未知操作类型:" + s.getOperateType()));
            }));
        }
        return result;
    }
}
