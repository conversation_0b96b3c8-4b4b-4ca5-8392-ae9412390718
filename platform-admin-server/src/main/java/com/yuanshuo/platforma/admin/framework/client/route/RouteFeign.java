package com.yuanshuo.platforma.admin.framework.client.route;

import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platforma.admin.framework.client.dto.CustomerLocationRelationEditDTO;
import com.yuanshuo.platforma.admin.framework.client.dto.CustomerLocationRelationPageDTO;
import com.yuanshuo.platforma.admin.framework.client.vo.CustomerLocationRelationPageVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "trade-route")
public interface RouteFeign {

    /**
     * 分页查询
     */
    @PostMapping("/rpc/customerLocationRelation/pageList")
    R<List<CustomerLocationRelationPageVO>> pageList(@RequestBody CustomerLocationRelationPageDTO dto);

    /**
     * 编辑
     */
    @PostMapping("/rpc/customerLocationRelation/editById")
    R<Void> editById(@RequestBody CustomerLocationRelationEditDTO dto);
}
