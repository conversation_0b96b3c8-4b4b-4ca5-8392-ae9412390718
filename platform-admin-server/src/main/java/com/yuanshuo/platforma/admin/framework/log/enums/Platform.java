package com.yuanshuo.platforma.admin.framework.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 消息来源
 */
@Getter
@AllArgsConstructor
public enum Platform {

    System("system", "管理后台"),
    A<PERSON>("app", "App"),
    XyzsMini("xyzs_mini_program", "小元智送小程序"),
    XyckMini("xyck_mini_program", "小元车控小程序"),
    ;
    private final String code;
    private final String value;

    public static Platform getByCode(String type) {
        if (type == null) {
            return null;
        }
        return Arrays.stream(Platform.class.getEnumConstants())
                .filter(e -> e.getCode().equals(type))
                .findFirst()
                .orElse(null);
    }
}
