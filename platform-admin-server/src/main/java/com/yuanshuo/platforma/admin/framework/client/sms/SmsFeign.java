package com.yuanshuo.platforma.admin.framework.client.sms;


import com.yuanshuo.platform.admin.common.core.domain.R;
import com.yuanshuo.platforma.admin.framework.client.dto.MessageSendDTO;
import com.yuanshuo.platforma.admin.framework.client.vo.MessageSendVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "platform-infra", contextId = "SmsFeign", fallbackFactory = SmsFactory.class)
public interface SmsFeign {
    /**
     * 单条发送 短信、邮件、推送等
     */
    @PostMapping("/feign/singleSend")
    R<MessageSendVO> singleSend(@RequestBody @Valid MessageSendDTO messageSendDTO);

}
