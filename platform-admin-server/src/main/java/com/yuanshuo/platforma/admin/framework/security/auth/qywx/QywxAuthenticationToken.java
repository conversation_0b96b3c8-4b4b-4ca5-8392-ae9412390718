package com.yuanshuo.platforma.admin.framework.security.auth.qywx;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public class QywxAuthenticationToken extends AbstractAuthenticationToken {


    private final Object principal; // openid

    public QywxAuthenticationToken(Object openId) {
        super(null);
        this.principal = openId;
        setAuthenticated(false);
    }

    public QywxAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        // 企业微信登录一般没有密码
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }
}
