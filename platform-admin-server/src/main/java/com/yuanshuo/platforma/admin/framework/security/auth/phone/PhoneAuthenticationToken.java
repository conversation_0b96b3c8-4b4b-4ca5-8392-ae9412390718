package com.yuanshuo.platforma.admin.framework.security.auth.phone;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class PhoneAuthenticationToken extends AbstractAuthenticationToken {
    private final Object principal; // openid

    public PhoneAuthenticationToken(Object openId) {
        super(null);
        this.principal = openId;
        setAuthenticated(false);
    }

    public PhoneAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        // 一般没有密码
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }
}
