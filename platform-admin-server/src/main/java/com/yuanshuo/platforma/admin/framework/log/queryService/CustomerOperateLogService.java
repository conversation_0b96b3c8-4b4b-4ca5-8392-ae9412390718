package com.yuanshuo.platforma.admin.framework.log.queryService;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuanshuo.common.entity.support.LoginUser;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.platform.admin.api.model.dto.CustomerQueryPageDTO;
import com.yuanshuo.platform.admin.api.model.dto.CustomerSaveDTO;
import com.yuanshuo.platform.admin.api.model.vo.CustomerVO;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.service.OperateLogBaseInterface;
import com.yuanshuo.platforma.admin.framework.web.service.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 客户操作日志服务
 */
@Service("customerOperateLogService")
public class CustomerOperateLogService implements OperateLogBaseInterface {

    @Autowired
    private CustomerService customerService;


    public Object getOperateObject(OperateLogEnum.Content content, Map<String, Object> reqPar, LoginUser loginUser) {
        CustomerSaveDTO customerSaveDTO = JSONObject.parseObject(JSON.toJSONString(reqPar), CustomerSaveDTO.class);
        CustomerQueryPageDTO queryDTO = new CustomerQueryPageDTO();
        queryDTO.setCustomerFullNameLike(customerSaveDTO.getCustomerFullName());
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(1);
        List<String> descFields = new ArrayList<>();
        descFields.add("create_time");
        queryDTO.setDescs(descFields);

        TableDataInfo<CustomerVO> tableDataInfo = customerService.queryCustomerPage(queryDTO);
        if (tableDataInfo != null && tableDataInfo.getRows() != null && tableDataInfo.getRows().size() > 0) {
            return tableDataInfo.getRows().get(0);
        }
        return null;
    }
}