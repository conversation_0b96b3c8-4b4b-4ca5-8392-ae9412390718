package com.yuanshuo.platforma.admin.framework.security.auth.qywx;

import cn.hutool.core.util.StrUtil;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth;
import com.yuanshuo.platform.admin.common.core.domain.model.LoginUser;
import com.yuanshuo.platform.admin.common.core.domain.model.QywxLoginBody;
import com.yuanshuo.platform.admin.api.enums.AuthType;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.common.exception.ServerErrorCode;
import com.yuanshuo.platform.admin.common.qywx.service.WxCpHelper;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.service.ISysUserAuthService;
import com.yuanshuo.platform.admin.domain.service.ISysUserService;
import com.yuanshuo.platform.admin.domain.service.impl.SysUserNewRepository;
import com.yuanshuo.platforma.admin.framework.web.service.SysPermissionService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

/**
 * 企业微信认证提供者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class QywxAuthenticationProvider implements AuthenticationProvider {
    @Autowired
    private SysUserNewRepository sysUserService;
    @Autowired
    private WxCpHelper wxCpHelper;

    @Autowired
    private ISysUserAuthService userAuthService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPermissionService permissionService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        QywxLoginBody body = (QywxLoginBody) authentication.getPrincipal();
        UserDetails userDetails = loadUserByQywxUserId(body);

        return new QywxAuthenticationToken(userDetails, userDetails.getAuthorities());
    }

    private UserDetails loadUserByQywxUserId(QywxLoginBody body) {

        // 销售小工具应用
        final WxCpService wxCpService = wxCpHelper.getCpService();

        // 获取企业微信用户ID根据授权code
        WxCpOauth2UserInfo authUserInfo = null;
        try {
            authUserInfo = wxCpService.getOauth2Service().getAuthUserInfo(body.getCode());
        } catch (WxErrorException e) {
            log.error("企业微信授权登录失败", e);
            throw ServerErrorCode.ERROR.exp("企业微信授权登录失败");
        }

        String qywxUserId = authUserInfo.getUserId();
        if (StrUtil.isEmpty(qywxUserId)) {
            throw ServerErrorCode.ERROR.exp("企业微信授权登录失败-非企业成员");
        }
        SysUserEntity sysUserEntity = sysUserService.selectUserByUserNameAndUserType(qywxUserId, UserType.Internal.getCode());
        if (null == sysUserEntity) {
            throw ServerErrorCode.ERROR.exp("请联系系统管理员开通企业微信登录方式");
        }
        
        // 检查用户认证状态
        if ("cancelled".equals(sysUserEntity.getAuthStatus())) {
            throw ServerErrorCode.ERROR.exp("系统账号已注销");
        }

        // 根据userId查询用户信息
        SysUser sysUser = userService.selectUserById(sysUserEntity.getUserId());
        if (null == sysUser || StrUtil.equals(sysUser.getDelFlag(), "2") || ! StrUtil.equals(sysUser.getStatus(), "0")) {
            throw ServerErrorCode.ERROR.exp("系统账号不存在或者已被禁用");
        }

        return createLoginUser(sysUser);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return QywxAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
    }
}
