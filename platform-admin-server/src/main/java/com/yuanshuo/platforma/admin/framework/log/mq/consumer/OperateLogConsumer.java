package com.yuanshuo.platforma.admin.framework.log.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.yuanshuo.common.mq.annotation.MQConsumer;
import com.yuanshuo.common.mq.base.AbstractMQPushConsumer;
import com.yuanshuo.common.mq.base.MessageInfo;
import com.yuanshuo.platform.admin.common.utils.TransferUtils;
import com.yuanshuo.platforma.admin.framework.log.model.OperateLogReq;
import com.yuanshuo.platforma.admin.framework.log.mq.model.OperateLogMQ;
import com.yuanshuo.platforma.admin.framework.log.service.OperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;


/**
 * 操作日志消息订阅
 */
@Slf4j
@MQConsumer(topic = "operate_log", tag = "log_sync")
public class OperateLogConsumer extends AbstractMQPushConsumer {

    @Autowired
    private OperateLogService operateLogService;

    @Override
    public boolean process(MessageInfo message, Map extMap) {
        log.info("收到操作日志消息:{}", message);
        try {
            OperateLogMQ tradeOrderMQ = JSON.parseObject(message.getBody(), OperateLogMQ.class);
            if (tradeOrderMQ == null) {
                log.error("解析日志失败,body:{}", message.getBody());
                return true;
            }
            OperateLogReq req = TransferUtils.transfer(tradeOrderMQ, OperateLogReq::new);
            operateLogService.saveOperateLog(req);
        } catch (Exception e) {
            log.error("解析日志失败,{}", e.getMessage());
            return true;
        }
        return true;
    }

}
