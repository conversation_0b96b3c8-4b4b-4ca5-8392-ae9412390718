package com.yuanshuo.platforma.admin.framework.log.annotation;


import com.yuanshuo.platforma.admin.framework.log.enums.DataSource;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateType;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperateLog {
    //操作内容
    OperateLogEnum.Content name();

    //操作类型
    OperateType type();

    //填充数据来源
    DataSource dataSource() default DataSource.Front;

    //操作对象字段名称
    String field() default "";

    //操作对象函数类名
    String operationObjectClass() default "";

}
