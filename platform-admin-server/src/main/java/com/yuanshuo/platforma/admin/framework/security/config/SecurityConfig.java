package com.yuanshuo.platforma.admin.framework.security.config;

import com.yuanshuo.platforma.admin.framework.security.auth.phone.PhoneAuthenticationProvider;
import com.yuanshuo.platforma.admin.framework.security.auth.qywx.QywxAuthenticationProvider;
import com.yuanshuo.platforma.admin.framework.security.auth.wx.WxAuthenticationProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import com.yuanshuo.platforma.admin.framework.security.filter.JwtAuthenticationTokenFilter;
import com.yuanshuo.platforma.admin.framework.security.handler.AuthenticationEntryPointHandler;
import com.yuanshuo.platforma.admin.framework.security.handler.UserLogoutSuccessHandler;

import java.util.Arrays;
import java.util.List;

/**
 * spring security配置
 */
@EnableWebSecurity
@Configuration
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    /**
     * 自定义用户认证逻辑
     */
    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 认证失败处理类
     */
    @Autowired
    private AuthenticationEntryPointHandler unauthorizedHandler;

    /**
     * 退出处理类
     */
    @Autowired
    private UserLogoutSuccessHandler userLogoutSuccessHandler;

    /**
     * token认证过滤器
     */
    @Autowired
    private JwtAuthenticationTokenFilter authenticationTokenFilter;

    /**
     * 微信小程序登录提供方
     */
    @Autowired
    private WxAuthenticationProvider wxAuthenticationProvider;

    /**
     * 企业微信登录提供方
     */
    @Autowired
    private QywxAuthenticationProvider qywxAuthenticationProvider;

    /**
     * 手机号登录提供方
     */
    @Autowired
    private PhoneAuthenticationProvider phoneAuthenticationProvider;


    /**
     * 身份验证实现
     */
    @Bean
    public AuthenticationManager authenticationManager(PasswordEncoder passwordEncoder) {
        return new ProviderManager(authenticationProviders(passwordEncoder));
    }

    private List<AuthenticationProvider> authenticationProviders(PasswordEncoder passwordEncoder) {
        return Arrays.asList(
                // 账号密码登录
                daoAuthenticationProvider(passwordEncoder),
                // 小程序授权登录
                wxAuthenticationProvider,
                // 企业微信授权登录
                qywxAuthenticationProvider,
                // 手机号登录
                phoneAuthenticationProvider
        );
    }

    private DaoAuthenticationProvider daoAuthenticationProvider(PasswordEncoder passwordEncoder) {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(userDetailsService);
        provider.setPasswordEncoder(passwordEncoder);
        return provider;
    }

    @Bean
    protected SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception
    {
        return httpSecurity
                // CSRF禁用，因为不使用session
                .csrf(csrf -> csrf.disable())
                // 禁用HTTP响应标头
                .headers((headersCustomizer) -> {
                    headersCustomizer.cacheControl(cache -> cache.disable()).frameOptions(options -> options.sameOrigin());
                })
                // 认证失败处理类
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                // 基于token，所以不需要session
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 注解标记允许匿名访问的url
                .authorizeHttpRequests((requests) -> {
                    // 对于登录login 注册register 验证码captchaImage 允许匿名访问
                    requests.antMatchers("/app/phoneLoginForXyck","/login", "/register", "/captchaImage", "/wxLogin", "/qywxLogin","/qywxLoginCallback", "/getQywxWebLoginUrl", "/sendSmsCode", "/phoneLogin", "/app/phoneLogin", "/app/sendSmsCode", "/app/phoneLogin").permitAll()
                            // 静态资源，可匿名访问
                            .antMatchers(HttpMethod.GET, "/", "/*.html", "/**/*.html", "/**/*.css", "/**/*.js", "/profile/**").permitAll()
                            .antMatchers("/swagger-ui.html", "/swagger-resources/**", "/webjars/**", "/*/api-docs", "/druid/**").permitAll()
                            .antMatchers("/admin/rpc/**").permitAll()
                            // 除上面外的所有请求全部需要鉴权认证
                            .anyRequest().authenticated();
                })
                // 添加Logout filter
                .logout(logout -> logout.logoutUrl("/logout").logoutSuccessHandler(userLogoutSuccessHandler))
                // 添加JWT filter
                .addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class)
                .build();
    }

    /**
     * 强散列哈希加密实现
     */
    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
