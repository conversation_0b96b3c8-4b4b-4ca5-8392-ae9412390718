package com.yuanshuo.platforma.admin.framework.log.service;

import com.yuanshuo.common.entity.support.LoginUser;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class OperateLogBaseService implements OperateLogBaseInterface {
    public Object getOperateObject(OperateLogEnum.Content content, Map<String, Object> reqPar, LoginUser loginUser) {

        return null;
    }
}
