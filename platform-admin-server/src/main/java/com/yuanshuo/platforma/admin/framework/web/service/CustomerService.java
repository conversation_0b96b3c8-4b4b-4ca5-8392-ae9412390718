package com.yuanshuo.platforma.admin.framework.web.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanshuo.common.entity.exception.BusinessException;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.*;
import com.yuanshuo.platform.admin.domain.entity.Customer;
import com.yuanshuo.platform.admin.domain.service.ICustomerService;
import com.yuanshuo.platform.admin.domain.service.query.CustomerQuery;
import com.yuanshuo.platform.admin.api.model.vo.CustomerDetailVO;
import com.yuanshuo.platform.admin.api.model.vo.CustomerVO;
import com.yuanshuo.platform.order.api.feign.AdministrativeDivisionFeign;
import com.yuanshuo.platform.order.api.feign.CargoFeign;
import com.yuanshuo.platform.order.api.feign.ContractFeign;
import com.yuanshuo.platform.order.api.model.dto.query.AdministrativeDivisionQueryDTO;
import com.yuanshuo.platform.order.api.model.dto.query.ContractListQueryDTO;
import com.yuanshuo.platform.order.api.model.vo.CargoListResponse;
import com.yuanshuo.platform.order.api.model.vo.ContractVO;
import com.yuanshuo.platforma.admin.framework.client.dto.CustomerLocationRelationEditDTO;
import com.yuanshuo.platforma.admin.framework.client.dto.CustomerLocationRelationPageDTO;
import com.yuanshuo.platforma.admin.framework.client.route.RouteFeign;
import com.yuanshuo.platforma.admin.framework.client.vo.CustomerLocationRelationPageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CustomerService {

    @Autowired
    private ICustomerService iCustomerService;

    @Autowired
    private AdministrativeDivisionFeign administrativeDivisionFeign;

    @Autowired
    private ContractFeign contractFeign;

    @Autowired
    private RouteFeign routeFeign;

    @Autowired
    private CargoFeign cargoFeign;

    @Transactional(rollbackFor = Exception.class)
    public void saveCustomer(CustomerSaveDTO dto) {

        // DTO 转 Entity
        Customer customer = new Customer();
        BeanUtils.copyProperties(dto, customer);

        // 1. 生成客户ID（按业务规则生成，例如：CUS+时间戳+随机数）
        String customerId = "CUS" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4);
        customer.setCustomerId(customerId);

        // 2. 生成月结账号（按业务规则生成）
        String monthlyAccount = generateMonthlyAccount(customerId);
        customer.setMonthlyAccount(monthlyAccount);

        // 3. 设置默认值（如果DTO中未传值）
        if (customer.getStatus() == null) {
            customer.setStatus(1); // 默认启用
        }
        if (customer.getCustomerType() == null) {
            customer.setCustomerType(1); // 默认企业
        }
        
        customer.setCreateTime(LocalDateTime.now());
        customer.setUpdateTime(LocalDateTime.now());
        customer.setIsDelete(0); // 默认未删除
        customer.setStationIds(String.join(",", dto.getStationIdList()));


        // 4. 保存客户信息
        try {
            int rows = iCustomerService.insert(customer);
        } catch (DuplicateKeyException e) {
            throw new BusinessException("客户全称不能重复");
        }

        updateStationIdsByCustomerId(dto.getStationIdList(), customerId);

    }

    private void updateStationIdsByCustomerId(List<String> stationIds, String customerId) {
        CustomerLocationRelationEditDTO customerLocationRelationPageDTO = new CustomerLocationRelationEditDTO();
        customerLocationRelationPageDTO.setCustomerId(customerId);
        customerLocationRelationPageDTO.setLocationIds(stationIds.stream().map(Long::parseLong).collect(Collectors.toList()));
        log.info("更新客户关联的站点信息，参数：{}", JSONObject.toJSONString(customerLocationRelationPageDTO));
        R<Void> voidR = routeFeign.editById(customerLocationRelationPageDTO);
        if (!voidR.isOk()) {
            log.error("创建或更新客户失败：{}", voidR.getMsg());
            throw new BusinessException("创建或更新客户失败");
        }
    }

    /**
     * 生成月结账号（格式示例：MA+客户ID后8位）
     */
    private String generateMonthlyAccount(String customerId) {
        String suffix = customerId.substring(Math.max(0, customerId.length() - 8));
        return "MA" + suffix;
    }

    public TableDataInfo<CustomerVO> queryCustomerPage(CustomerQueryPageDTO queryDTO) {

        // 1. 构建分页对象
        Page<Customer> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());

        // 2. 构建查询条件（所有条件为“且”关系）
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();

        // 2.1 筛选条件：行业（非null时才加入条件）
        if (queryDTO.getIndustry() != null) {
            queryWrapper.eq(Customer::getIndustry, queryDTO.getIndustry());
        }

        // 2.2 筛选条件：城市（非空时才加入条件）
        if (StringUtils.hasText(queryDTO.getCityCode())) {
            queryWrapper.eq(Customer::getCityCode, queryDTO.getCityCode());
        }

        // 2.3 筛选条件：客户性质（非null时才加入条件）
        if (queryDTO.getCustomerType() != null) {
            queryWrapper.eq(Customer::getCustomerType, queryDTO.getCustomerType());
        }

        // 2.4 搜索条件：客户全称（模糊搜索，非空时才加入条件）
        if (StringUtils.hasText(queryDTO.getCustomerFullNameLike())) {
            queryWrapper.like(Customer::getCustomerFullName, queryDTO.getCustomerFullNameLike());
        }

        // 2.5 固定条件：只查询未删除的客户
        queryWrapper.eq(Customer::getIsDelete, 0);

        // 2.6 排序：默认按创建时间降序
        queryWrapper.orderByDesc(Customer::getCreateTime);

        // 3. 执行分页查询
        IPage<Customer> customerPage = iCustomerService.selectPage(page, queryWrapper);

        // 4. 转换为VO（处理枚举描述、城市名称等）
        Map<String, String> mapR = getCityNameByCode(customerPage.getRecords());

        Map<Long, CargoListResponse> cargoMap= getCargoMap();

        List<CustomerVO> voList = customerPage.getRecords().stream()
                .map(customer->convertToVO(customer, mapR,cargoMap))
                .collect(Collectors.toList());

        TableDataInfo<CustomerVO> tableData = new TableDataInfo<>();
        tableData.setTotal(page.getTotal());
        tableData.setRows(voList);
        return tableData;
    }

    private Map<Long, CargoListResponse> getCargoMap() {
        R<List<CargoListResponse>> list = cargoFeign.list();
        List<CargoListResponse> data = list.getData();
        return data.stream().collect(Collectors.toMap(CargoListResponse::getId, cargo -> cargo));
    }

    private Map<String, String> getCityNameByCode(List<Customer> customerList) {
        List<String> cityCodeList =customerList.stream().map(Customer::getCityCode).collect(Collectors.toList());
        AdministrativeDivisionQueryDTO divisionQueryDTO = new AdministrativeDivisionQueryDTO();
        divisionQueryDTO.setCodes(cityCodeList);
        return administrativeDivisionFeign.convertCodeToName(divisionQueryDTO).getData();
    }

    private CustomerVO convertToVO(Customer customer, Map<String, String> cityMap, Map<Long, CargoListResponse> cargoMap) {
        CustomerVO vo = new CustomerVO();
        BeanUtils.copyProperties(customer, vo);

        // 状态转换（1-启用，0-禁用）
        vo.setStatusDesc(customer.getStatus() == 1 ? "启用" : "禁用");

        // 客户性质转换（1-企业，0-个人）
        vo.setCustomerTypeDesc(customer.getCustomerType() == 1 ? "企业" : "个人");

        // 行业转换（1-快递，2-生鲜...8-其他）
        vo.setIndustryDesc(getIndustryDesc(customer.getIndustry(),cargoMap));

        // 城市编码转名称（实际项目中应从城市字典表获取）
        vo.setCityName(getCityNameByCode(customer.getCityCode(),cityMap));

        // 拆分点位ID列表
        if (customer.getStationIds() != null && !customer.getStationIds().isEmpty()) {
            vo.setStationList(Arrays.asList(customer.getStationIds().split(",")));
        }
        vo.setCreateTime(customer.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return vo;
    }
    // 行业编码转描述
    private String getIndustryDesc(Integer industryCode, Map<Long, CargoListResponse> cargoMap) {
        if (industryCode == null) return "";
        CargoListResponse cargoListResponse = cargoMap.get(Long.valueOf(industryCode));
        return cargoListResponse == null ? "" : cargoListResponse.getCargoType();
    }

    // 城市编码转名称（示例实现，实际应从数据库获取）
    private String getCityNameByCode(String cityCode,Map<String, String> cityMap) {
        return cityMap.getOrDefault(cityCode, cityCode);
    }

    public CustomerDetailVO detail(CustomerDetailDTO customerDetailDTO) {
        // 1. 查询客户基本信息
        Customer customer = iCustomerService.selectByCustomerId(customerDetailDTO.getCustomerId());
        if (customer == null || customer.getIsDelete() == 1) {
            throw new BusinessException("客户不存在或已删除");
        }

        // 2. 转换为详情VO
        CustomerDetailVO detailVO = new CustomerDetailVO();
        BeanUtils.copyProperties(customer, detailVO);


        // 5. 计算月结账号状态
        boolean isMonthlyValid = calculateMonthlyAccountStatus(customer);
        detailVO.setMonthlyAccountStatus(isMonthlyValid);
        detailVO.setMonthlyAccountStatusDesc(isMonthlyValid ? "生效中" : "已失效");

        detailVO.setStationList(Arrays.asList(customer.getStationIds().split(",")));

        List<CustomerDetailVO.StationVO> stationDetailList = getStaionDetailList(customer);

        detailVO.setStationDetailList(stationDetailList);
        detailVO.setCreateTime(customer.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return detailVO;
    }

    private List<CustomerDetailVO.StationVO> getStaionDetailList(Customer customer) {


        List<CustomerDetailVO.StationVO> resultList = new ArrayList<>();
        CustomerLocationRelationPageDTO dto = new CustomerLocationRelationPageDTO();
        dto.setCustomerId(customer.getCustomerId());

        R<List<CustomerLocationRelationPageVO>> response = routeFeign.pageList(dto);

        if (!response.isOk() ) {
            log.error("查询失败，customerId={}", customer.getCustomerId());
            return resultList; // 失败时退出循环
        }

        List<CustomerLocationRelationPageVO> pageList = response.getData();
        // 转换并添加到结果列表
        pageList.forEach(item -> {
            CustomerDetailVO.StationVO stationVO = convertToStationVO(item);
            resultList.add(stationVO);
        });
        return resultList;
    }

    private CustomerDetailVO.StationVO convertToStationVO(CustomerLocationRelationPageVO item) {
        CustomerDetailVO.StationVO stationVO = new CustomerDetailVO.StationVO();
        stationVO.setId(item.getLocationId());
        stationVO.setLocationName(item.getLocationName());
        stationVO.setAddress(item.getLocationAddress());
        stationVO.setLocationType(item.getLocationType());
        stationVO.setLocationTypeName(item.getLocationTypeName());
        stationVO.setStatus(item.getStatus());
        stationVO.setStatusName(item.getStatusName());
        return stationVO;
    }

    // 辅助方法：计算月结账号状态
    private boolean calculateMonthlyAccountStatus(Customer customer) {
        // 条件1：客户状态为启用
        if (customer.getStatus() != 1) {
            return false;
        }
        // 条件2：存在已通过的关联合同
        ContractListQueryDTO contractListQueryDTO = new ContractListQueryDTO();
        contractListQueryDTO.setCustomerId(customer.getCustomerId());
        contractListQueryDTO.setStatus(1);
        List<ContractVO> data = contractFeign.getContractListByConditions(contractListQueryDTO).getData();
        if(CollectionUtils.isEmpty(data)){
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCustomer(CustomerUpdateDTO dto) {
        // 1. 更新客户主表
        Customer customer = iCustomerService.selectByCustomerId(dto.getCustomerId());
        if (customer == null) {
            throw new BusinessException("客户不存在或已删除");
        }
        BeanUtils.copyProperties(dto, customer);
        customer.setCityCode(dto.getCityCode());
        customer.setUpdateTime(LocalDateTime.now());
        customer.setStationIds(String.join(",", dto.getStationIdList()));


        try {
            iCustomerService.updateById(customer);
        } catch (DuplicateKeyException e) {
            throw new BusinessException("客户全称不能重复");
        }

        updateStationIdsByCustomerId(dto.getStationIdList(), customer.getCustomerId());

    }


    public List<CustomerVO> queryCustomerList(CustomerQueryListDTO queryDTO) {
        CustomerQuery customerQuery = BeanUtil.copyProperties(queryDTO, CustomerQuery.class);
        List<Customer> customerList = iCustomerService.queryCustomerList(customerQuery);
        Map<String, String> mapR = getCityNameByCode(customerList);
        Map<Long, CargoListResponse> cargoMap = getCargoMap();
        List<CustomerVO> customerVOList = customerList.stream().map(customer -> convertToVO(customer, mapR, cargoMap)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(customerVOList)){
            ContractListQueryDTO contractListQueryDTO = new ContractListQueryDTO();
            contractListQueryDTO.setStatus(1);
            List<ContractVO> contractVOList = contractFeign.getContractListByConditions(contractListQueryDTO).getData();
            // 提取所有有效合同的customerId，存入Set便于快速查询
            Set<String> contractCustomerIds = contractVOList.stream()
                    .map(ContractVO::getCustomerId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            customerVOList.forEach(customerVO -> {
                if (contractCustomerIds.contains(customerVO.getCustomerId())) {
                    customerVO.setValidContractBound(true);
                }
            });
        }
        return customerVOList;
    }
}
