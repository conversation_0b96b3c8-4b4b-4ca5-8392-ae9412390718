package com.yuanshuo.platforma.admin.framework.log.mq.model;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OperateLogMQ {

    /**
     * 操作人账号
     */
    private String operateUserId;

    /**
     * 操作人名称
     */
    private String operateUserName;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作来源(web,app,)
     */
    private String source;

    /**
     * 操作模块
     */
    private String operateModule;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 描述(操作对象ID)
     */
    private String description;

    /**
     * 操作参数
     */
    private String param;

    /**
     * 请求路径
     */
    private String uri;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
