package com.yuanshuo.platforma.admin.framework.client.sms;

import com.yuanshuo.platform.admin.common.core.domain.R;
import com.yuanshuo.platforma.admin.framework.client.dto.MessageSendDTO;
import com.yuanshuo.platforma.admin.framework.client.vo.MessageSendVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class SmsFactory implements FallbackFactory<SmsFeign> {
    @Override
    public SmsFeign create(Throwable cause) {
        return new SmsFeign() {
            @Override
            public R<MessageSendVO> singleSend(MessageSendDTO messageSendDTO) {
                return R.fail("调用公共开放平台失败" + cause.getMessage());
            }
        };
    }
}
