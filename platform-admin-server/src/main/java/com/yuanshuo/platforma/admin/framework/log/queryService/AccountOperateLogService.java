package com.yuanshuo.platforma.admin.framework.log.queryService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuanshuo.common.entity.support.LoginUser;
import com.yuanshuo.platform.admin.api.model.dto.AccountCreateDTO;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.service.impl.SysUserNewRepository;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.service.OperateLogBaseInterface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 账户操作日志服务
 */
@Service("accountOperateLogService")
public class AccountOperateLogService implements OperateLogBaseInterface {

    @Autowired
    private SysUserNewRepository sysUserService;

    public Object getOperateObject(OperateLogEnum.Content content, Map<String, Object> reqPar, LoginUser loginUser) {
        AccountCreateDTO createDTO = JSONObject.parseObject(JSON.toJSONString(reqPar), AccountCreateDTO.class);
        // 检查手机号是否已存在且用户类型为C
        SysUserEntity sysUserEntity = sysUserService.selectUserByPhoneAndUserType(createDTO.getPhonenumber(), UserType.C.getCode());
        return sysUserEntity;
    }
}