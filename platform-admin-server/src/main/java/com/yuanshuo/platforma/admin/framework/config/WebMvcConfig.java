//package com.yuanshuo.platforma.admin.framework.config;
//
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// */
//@Configuration
//public class WebMvcConfig implements WebMvcConfigurer {
//
////    @Resource
////    private AuthInterceptor authInterceptor;
////
////    @Override
////    public void addInterceptors(InterceptorRegistry registry) {
////        // 加入自定义的拦截器
////        registry.addInterceptor(authInterceptor).addPathPatterns("/**");
////    }
//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler("/img/**")
//                .addResourceLocations("classpath:/img/");
//    }
//
//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
////        registry.addMapping("/**")
////                .allowedOrigins("*")
////                .allowedMethods("GET", "POST", "PUT", "DELETE")
////                .allowedHeaders("*")
////                .maxAge(3600);
//    }
//
//
//
//}
