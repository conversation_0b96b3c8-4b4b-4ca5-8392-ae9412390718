package com.yuanshuo.platforma.admin.framework.security.auth.phone;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth;
import com.yuanshuo.platform.admin.common.core.domain.model.LoginUser;
import com.yuanshuo.platform.admin.common.core.domain.model.req.PhoneLoginReq;
import com.yuanshuo.platform.admin.api.enums.AuthType;
import com.yuanshuo.platform.admin.common.enums.UserStatus;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.common.exception.ServerErrorCode;
import com.yuanshuo.platform.admin.common.exception.ServiceException;
import com.yuanshuo.platform.admin.common.utils.SecurityUtils;
import com.yuanshuo.platform.admin.common.utils.StringUtils;
import com.yuanshuo.platform.admin.common.utils.TransferUtils;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.service.ISysUserAuthService;
import com.yuanshuo.platform.admin.domain.service.impl.SysUserNewRepository;
import com.yuanshuo.platforma.admin.framework.web.service.SysPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 手机号登录提供方
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PhoneAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private ISysUserAuthService userAuthService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private SysUserNewRepository sysUserService;

    @Autowired
    private ISysUserAuthService sysUserAuthService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        PhoneLoginReq body = (PhoneLoginReq) authentication.getPrincipal();
        UserDetails userDetails = loadUserByPhoneUserId(body);

        return new PhoneAuthenticationToken(userDetails, userDetails.getAuthorities());
    }

    private UserDetails loadUserByPhoneUserId(PhoneLoginReq body) {

        // 根据手机号和用户类型查找重复用户
        SysUserEntity existingUser = sysUserService.selectUserByPhoneAndUserType(body.getPhone(), UserType.C.getCode());
        SysUserEntity sysUser = null;

        if (existingUser != null) {
            // 重复用户，检查auth_status
            if ("inactive".equals(existingUser.getAuthStatus())) {
                // 未激活状态，修改为未实名
                existingUser.setAuthStatus("unverified");
                existingUser.setUpdateTime(LocalDateTime.now());
                sysUserService.updateById(existingUser);
            }
            sysUser = existingUser;
        } else {
            // 创建用户信息
            sysUser = new SysUserEntity();
            sysUser.setNickName("元朔用户" + StringUtils.right(body.getPhone(), 4));
            sysUser.setSex("2");
            sysUser.setUserName(body.getPhone());
            sysUser.setPassword(SecurityUtils.encryptPassword("123456"));
            sysUser.setPhonenumber(body.getPhone());
            sysUser.setUserType(UserType.C.getCode());
            sysUser.setPlatform(body.getPlatform().getCode());
            sysUser.setCreateTime(LocalDateTime.now());
            sysUser.setUpdateTime(LocalDateTime.now());
            sysUser.setAuthStatus("unverified");
            sysUserService.save(sysUser);
        }
        if (UserStatus.DELETED.getCode().equals(sysUser.getDelFlag())) {
            throw ServerErrorCode.WARN.exp("用户已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("账号已停用");
        }
        if ("cancelled".equals(sysUser.getAuthStatus())) {
            throw new ServiceException("账号已注销");
        }

        return createLoginUser(TransferUtils.transfer(sysUser, SysUser::new));
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return PhoneAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
    }
}
