package com.yuanshuo.platforma.admin.framework.web.service;


import com.alibaba.fastjson2.JSONObject;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.qywx.config.WxCpProperties;
import com.yuanshuo.platform.admin.domain.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WxSendMessageService {

    @Autowired
    private WxCpProperties properties;

    @Autowired
    private ISysUserService sysUserService;


    public void sendTextMessage(Long userId,String content) {

        // 初始化企业微信配置
        WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
        config.setCorpId(properties.getCorpId()); // 替换为实际企业ID
        config.setAgentId(properties.getAppConfigs().get(1).getAgentId()); // 替换为应用ID
        config.setCorpSecret(properties.getAppConfigs().get(1).getSecret()); // 替换为应用密钥

        // 创建WxCpService实例
        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(config);

        // 获取消息服务实例
        WxCpMessageService messageService = wxCpService.getMessageService();


        List<SysUser> sysUsers = sysUserService.selectNewUserList(Collections.singletonList(userId),null,null);
        try {
            // 构建文本消息
            WxCpMessage message = WxCpMessage.TEXT()
                    .agentId(config.getAgentId()) // 应用ID
                    .toUser(sysUsers.get(0).getUserName()) // 接收用户ID，多个用|分隔，@all表示全体成员
                    .content(content) // 消息内容
                    .build();

            // 发送消息
            WxCpMessageSendResult result = messageService.send(message);
            System.out.println("消息发送结果：" + result);
        } catch (WxErrorException e) {
            e.printStackTrace();
            System.err.println("消息发送失败：" + e.getError().getErrorMsg());
        }
    }


    public List<JSONObject> getUser() {
        // 1. 初始化配置（同之前）
        WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
        config.setCorpId(properties.getCorpId());
        config.setCorpSecret(properties.getAppConfigs().get(2).getSecret()); // 需有通讯录权限
        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(config);

        try {
            // 2. 构建请求参数（空对象，接口无需额外参数）
            JSONObject request = new JSONObject();
            request.put("limit",10000);
            // 3. 调用 list_id 接口
            String response = wxCpService.post("https://qyapi.weixin.qq.com/cgi-bin/user/list_id", request.toString());

            // 4. 解析响应
            JSONObject result = JSONObject.parseObject(response);
            if (result.getIntValue("errcode") == 0) {
                // 获取成员ID列表（包含userid和department）
                List<JSONObject> userList = result.getJSONArray("dept_user").toJavaList(JSONObject.class);
                // 遍历打印
                for (JSONObject user : userList) {
                    String userId = user.getString("userid");
                    Integer department = user.getInteger("department");
                    log.info("userId: {}, department: {}", userId, department);
                }
                return userList;

            } else {
                log.error("接口调用失败：" + result.getString("errmsg"));
                return null;
            }
        } catch (WxErrorException e) {
            log.error("接口调用异常：" + e);
            return null;
        }
    }

    public void syncWechatUsersToDb() {
        // 1. 获取企业微信用户列表（包含userid和department）
        List<JSONObject> userList = this.getUser();
        if (userList == null || userList.isEmpty()) {
            System.out.println("企业微信用户列表为空，无需同步");
            return;
        }

        // 2. 定义销售部门ID列表（需同步为销售人员的部门）
        List<Long> salesDeptIds = new ArrayList<>();
        salesDeptIds.addAll(Arrays.asList(15L, 28L, 29L, 30L));
        salesDeptIds.addAll(Arrays.asList(16L, 24L, 25L, 26L, 27L));
        salesDeptIds.addAll(Arrays.asList(17L, 23L));
        salesDeptIds.addAll(Arrays.asList(18L, 19L, 20L, 21L, 22L));
        salesDeptIds.addAll(Arrays.asList(88L, 89L, 90L));
        salesDeptIds.addAll(Arrays.asList(31L, 32L, 33L, 34L));

        // 3. 获取数据库用户列表，构建userName到SysUser的映射
        List<SysUser> sysUsers = sysUserService.selectNewUserList(null,null,null);
        Map<String, SysUser> sysUserMap = sysUsers.stream()
                .collect(Collectors.toMap(
                        SysUser::getUserName,
                        Function.identity(),
                        (existing, replacement) -> existing // 处理重复用户，保留第一个
                ));

        // 4. 遍历企业微信用户，执行同步逻辑
        for (JSONObject wechatUser : userList) {
            String userId = wechatUser.getString("userid"); // 企业微信用户ID
            Long deptId = wechatUser.getLong("department"); // 企业微信用户所属部门ID

            // 跳过无效数据（无userid或部门ID）
            if (userId == null || deptId == null) {
                continue;
            }

            // 5. 判断该用户是否属于销售部门
            Integer isSales = salesDeptIds.contains(deptId) ? 1 : 0;

            // 6. 检查数据库中是否存在该用户
            SysUser dbUser = sysUserMap.get(userId);
            if (dbUser != null) {
                // 6.1 数据库已存在：更新销售人员标记（isSalesperson）
                if (!Objects.equals(dbUser.getIsSalesperson(), isSales)) {
                    dbUser.setIsSalesperson(isSales);
                    sysUserService.updateUserSalesperson(dbUser);
                    System.out.println("更新用户：" + userId + "，销售人员标记：" + isSales);
                } else {
                    // 标记无变化，不处理
                    System.out.println("用户：" + userId + " 销售人员标记无变化，无需更新");
                }
            } else {
                // 6.2 数据库不存在：插入新用户，设置销售人员标记
                SysUser newUser = new SysUser();
                newUser.setUserName(userId); // 用户名=企业微信userid
                newUser.setIsSalesperson(isSales); // 标记是否为销售人员
                // 补充其他必填字段（根据实际表结构）
                newUser.setStatus("0"); // 状态：正常
                newUser.setCreateTime(new Date());
                newUser.setDelFlag("0");
                newUser.setRemark("数据同步插入");
                sysUserService.insertUserSalesperson(newUser);
                System.out.println("插入新用户：" + userId + "，部门：" + deptId + "，是否销售：" + isSales);
            }
        }

    }
}
