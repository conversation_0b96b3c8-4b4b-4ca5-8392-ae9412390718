package com.yuanshuo.platforma.admin.framework.web.service;

import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.util.ObjectUtil;
import com.yuanshuo.platform.admin.common.constant.CacheConstants;
import com.yuanshuo.platform.admin.common.constant.Constants;
import com.yuanshuo.platform.admin.common.constant.UserConstants;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.core.domain.model.LoginUser;
import com.yuanshuo.platform.admin.common.core.domain.model.QywxLoginBody;
import com.yuanshuo.platform.admin.common.core.domain.model.WxLoginBody;
import com.yuanshuo.platform.admin.common.core.domain.model.req.PhoneLoginReq;
import com.yuanshuo.platform.admin.common.core.domain.model.res.PhoneLoginRes;
import com.yuanshuo.platform.admin.common.core.redis.RedisCache;
import com.yuanshuo.platform.admin.api.enums.AuthType;
import com.yuanshuo.platform.admin.common.exception.ServerErrorCode;
import com.yuanshuo.platform.admin.common.exception.ServiceException;
import com.yuanshuo.platform.admin.common.exception.user.*;
import com.yuanshuo.platform.admin.common.qywx.service.WxCpHelper;
import com.yuanshuo.platform.admin.common.utils.DateUtils;
import com.yuanshuo.platform.admin.common.utils.MessageUtils;
import com.yuanshuo.platform.admin.common.utils.StringUtils;
import com.yuanshuo.platform.admin.common.utils.TransferUtils;
import com.yuanshuo.platform.admin.common.utils.ip.IpUtils;
import com.yuanshuo.platform.admin.domain.service.ISysConfigService;
import com.yuanshuo.platform.admin.domain.service.ISysUserService;
import com.yuanshuo.platforma.admin.framework.client.sms.SmsService;
import com.yuanshuo.platforma.admin.framework.config.SkipSmsValidateConfig;
import com.yuanshuo.platforma.admin.framework.config.SmsProperties;
import com.yuanshuo.platforma.admin.framework.manager.AsyncManager;
import com.yuanshuo.platforma.admin.framework.manager.factory.AsyncFactory;
import com.yuanshuo.platforma.admin.framework.model.MpLoginRes;
import com.yuanshuo.platforma.admin.framework.model.SysUserRes;
import com.yuanshuo.platforma.admin.framework.security.auth.phone.PhoneAuthenticationToken;
import com.yuanshuo.platforma.admin.framework.security.auth.qywx.QywxAuthenticationToken;
import com.yuanshuo.platforma.admin.framework.security.auth.wx.WxAuthenticationToken;
import com.yuanshuo.platforma.admin.framework.security.context.AuthenticationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SysLoginService {

    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private WxCpHelper wxCpHelper;

    @Autowired
    private SkipSmsValidateConfig skipSmsValidateConfig;

    @Autowired
    private SmsService smsService;

    @Resource
    private SmsProperties smsProperties;
    @Value("${phone.checkWhiteList}")
    private String phoneWhite;

    /**
     * 获取手机号白名单配置
     * 
     * @return 手机号白名单列表
     */
    public java.util.List<String> getPhoneWhiteList() {
        if (StringUtils.isEmpty(phoneWhite)) {
            return new ArrayList<>();
        }
        return Arrays.stream(phoneWhite.split(","))
                .map(String::trim)
                .filter(phone -> !StringUtils.isEmpty(phone))
                .collect(java.util.stream.Collectors.toList());
    }
    /**
     * 企业微信的授权code登录
     */
    public Object qywxLogin(QywxLoginBody body) {
        QywxAuthenticationToken authenticationToken = new QywxAuthenticationToken(body);
        AuthenticationContextHolder.setContext(authenticationToken);
        Authentication authentication = authenticationManager.authenticate(authenticationToken);
        // 用户信息
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        loginUser.setAuthType(AuthType.WeCom.name());
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        recordLoginInfo(loginUser.getUserId());
        // 清楚上下文联系
        AuthenticationContextHolder.clearContext();
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 微信授权登录
     */
    public MpLoginRes wxLogin(WxLoginBody wxLoginBody) {
        WxAuthenticationToken authenticationToken = new WxAuthenticationToken(wxLoginBody);
        AuthenticationContextHolder.setContext(authenticationToken);
        Authentication authentication = authenticationManager.authenticate(authenticationToken);
        // 用户信息
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        String token = tokenService.createToken(loginUser);
        SysUserRes sysUserRes = TransferUtils.transfer(loginUser.getUser(), SysUserRes::new);
        MpLoginRes mpLoginRes = new MpLoginRes();
        mpLoginRes.setAuthorization(token);
        mpLoginRes.setSysUser(sysUserRes);
        // 清楚上下文联系
        AuthenticationContextHolder.clearContext();
        WxMaConfigHolder.remove();

        return mpLoginRes;
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha)) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    /**
     * 获取企业微信登录地址
     *
     * @param env 环境
     * @return
     */
    public String getQywxWebLoginUrl(String env) {
        return wxCpHelper.getQywxWebLoginUrl(env);
    }

    /**
     * 获取回调地址
     *
     * @param env 环境
     * @return
     */
    public String getCallbackUrl(String env) {
        return wxCpHelper.getCallbackUrl(env);
    }

    public void sendSmsCode(String phone) {
        // 生成6位验证码
        String code = String.format("%06d", new Random().nextInt(999999));
        if (!smsProperties.isEnabled()) {
            code = "666666";
        }
        redisCache.setCacheObject(CacheConstants.LOGIN_SMS_CODE_KEY + phone, code, 5, TimeUnit.MINUTES);
        log.info("phone:{}, 验证码:{}",phone, code);
        // 调用短信服务发送验证码（此处为伪代码）
        smsService.send(phone, code);

    }

    public PhoneLoginRes loginByPhone(PhoneLoginReq request) {

        String phone = request.getPhone();
        String inputCode = request.getCode();
        boolean contains = Arrays.asList(phoneWhite.split(",")).contains(phone);
        if (request.getCheckWhiteList() && !contains) {
            throw ServerErrorCode.ERROR.exp("手机号不在白名单中");
        }
        if (skipSmsValidateConfig.isEnabled()) {
            SkipSmsValidateConfig.PhoneCodePair matched = skipSmsValidateConfig.getUsers().stream()
                    .filter(u -> u.getPhone().equals(phone))
                    .findFirst()
                    .orElse(null);
            if (matched != null && matched.getCode().equals(inputCode)) {
                // 匹配到跳过验证的手机号并且验证码正确，直接通过
                log.info("手机号 {} 跳过验证码校验", phone);
            } else {
                // 没有匹配到或验证码错误
                Object cacheCode = redisCache.getCacheObject(CacheConstants.LOGIN_SMS_CODE_KEY + phone);
                if (!ObjectUtil.equals(cacheCode, inputCode)) {
                    throw ServerErrorCode.ERROR.exp("验证码错误");
                }
            }
        } else {
            // 正常验证码校验
            Object cacheCode = redisCache.getCacheObject(CacheConstants.LOGIN_SMS_CODE_KEY + phone);
            if (!ObjectUtil.equals(cacheCode, inputCode)) {
                throw ServerErrorCode.ERROR.exp("验证码错误");
            }
        }

        PhoneAuthenticationToken authenticationToken = new PhoneAuthenticationToken(request);
        AuthenticationContextHolder.setContext(authenticationToken);
        Authentication authentication = authenticationManager.authenticate(authenticationToken);
        // 用户信息
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        loginUser.setAuthType(AuthType.Phone.name());
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        recordLoginInfo(loginUser.getUserId());
        // 清楚上下文联系
        AuthenticationContextHolder.clearContext();

        String token = tokenService.createToken(loginUser);
        PhoneLoginRes res = new PhoneLoginRes();
        res.setToken(token);
        res.setNickName(loginUser.getUser() != null ? loginUser.getUser().getNickName() : "");
        res.setUserId(loginUser.getUserId());
        // 生成token
        return res;

    }



    /**
     * 发送注销验证码
     * 
     * @param phoneNumber 手机号
     */
    public void sendCancelSmsCode(String phoneNumber) {
        // 生成6位验证码
        String code = String.format("%06d", new Random().nextInt(999999));
        if (!smsProperties.isEnabled()) {
            code = "666666";
        }
        redisCache.setCacheObject(CacheConstants.CANCEL_SMS_CODE_KEY + phoneNumber, code, 5, TimeUnit.MINUTES);
        log.info("注销验证码 - phone:{}, 验证码:{}", phoneNumber, code);
        // 调用短信服务发送验证码
        smsService.send(phoneNumber, code);
    }

    /**
     * 验证注销短信验证码
     * 
     * @param phoneNumber 手机号
     * @param smsCode 短信验证码
     * @throws ServiceException 验证失败时抛出异常
     */
    public void validateCancelSmsCode(String phoneNumber, String smsCode) {
        if (skipSmsValidateConfig.isEnabled()) {
            SkipSmsValidateConfig.PhoneCodePair matched = skipSmsValidateConfig.getUsers().stream()
                    .filter(u -> u.getPhone().equals(phoneNumber))
                    .findFirst()
                    .orElse(null);
            if (matched != null && matched.getCode().equals(smsCode)) {
                // 匹配到跳过验证的手机号并且验证码正确，直接通过
                log.info("手机号 {} 跳过注销验证码校验", phoneNumber);
                return;
            } else if (matched != null) {
                // 匹配到跳过验证的手机号但验证码错误
                throw ServerErrorCode.ERROR.exp("验证码错误");
            }
            // 没有匹配到跳过验证的手机号，继续正常验证
        }
        
        // 正常验证码校验
        Object cacheCode = redisCache.getCacheObject(CacheConstants.CANCEL_SMS_CODE_KEY + phoneNumber);
        if (!ObjectUtil.equals(cacheCode, smsCode)) {
            throw ServerErrorCode.ERROR.exp("验证码错误");
        }
        
        // 验证成功后删除验证码
        redisCache.deleteObject(CacheConstants.CANCEL_SMS_CODE_KEY + phoneNumber);
    }
}
