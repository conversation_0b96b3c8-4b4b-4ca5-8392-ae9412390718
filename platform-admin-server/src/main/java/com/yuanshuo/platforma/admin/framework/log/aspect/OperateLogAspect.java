package com.yuanshuo.platforma.admin.framework.log.aspect;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuanshuo.common.entity.support.LoginUser;
import com.yuanshuo.platform.admin.common.core.redis.RedisCache;
import com.yuanshuo.platforma.admin.framework.log.annotation.OperateLog;
import com.yuanshuo.platforma.admin.framework.log.enums.DataSource;
import com.yuanshuo.platforma.admin.framework.log.enums.OperateLogEnum;
import com.yuanshuo.platforma.admin.framework.log.enums.Platform;
import com.yuanshuo.platforma.admin.framework.log.model.OperateLogReq;
import com.yuanshuo.platforma.admin.framework.log.service.OperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.MethodParameter;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.core.annotation.SynthesizingMethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Component
@Aspect
@Order(-1)
public class OperateLogAspect {

    /***用户标识userKey*/
    public static final String LOGIN_TOKENS = "login_tokens:";
    private static final ParameterNameDiscoverer PARAMETER_NAME_DISCOVERER = new DefaultParameterNameDiscoverer();

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private OperateLogService operateLogService;


    @Pointcut("@annotation(com.yuanshuo.platforma.admin.framework.log.annotation.OperateLog)")
    public void pointCutService() {
    }

    @Around(value = "pointCutService()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获取OperateLog注解
        MethodSignature signature = (MethodSignature) point.getSignature();
        OperateLog operateLog = signature.getMethod().getAnnotation(OperateLog.class);
        if (operateLog == null) {
            return point.proceed();
        }

        // 获取Request请求
        HttpServletRequest request = null;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            request = attributes.getRequest();
        }
        if (null == request) {
            return point.proceed();
        }

        // 获取用户信息
        LoginUser loginUser = this.getLoginUser(request);
        if (null == loginUser) {
            return point.proceed();
        }
        //解析参数
        Map<String, Object> requestParams = this.requestArgumentResolver(point, request);

        //执行方法前获取数据
        Object beforeRes = this.getBeforeResByCondition(operateLog, requestParams, loginUser);

        // 执行方法
        Object result = point.proceed();

        try {
            //请求Attribute
            Map<String, Object> requestAttribute = this.requestAttributeResolver(request);

            //返回数据
            Object resultRes = result;
            if (result != null) {
                try {
                    JSONObject resJson = JSON.parseObject(JSON.toJSONString(result));
                    //操作失败不存日志
                    if (!"200".equals(resJson.get("code").toString())) {
                        return result;
                    }
                    resultRes = resJson.get("data");
                } catch (Exception e) {
                    resultRes = null;
                }
            }

            //执行方法后获取数据
            Object afterRes = this.getAfterResByCondition(operateLog, requestParams, loginUser);

            // 保存记录日志
            this.operateLogSave(beforeRes, afterRes, resultRes, requestParams, requestAttribute, operateLog, loginUser);
        } catch (Exception e) {
            log.error("保存日志失败：{}", e.getMessage());
        }

        return result;
    }

    /**
     * 保存操作日志
     *
     * @param beforeRes        执行前查询数据
     * @param afterRes         执行后查询数据
     * @param resultRes        返回数据
     * @param requestParams    查询参数
     * @param requestAttribute 属性
     * @param operateLog       配置参数
     * @param loginUser
     */
    private void operateLogSave(Object beforeRes, Object afterRes, Object resultRes, Map<String, Object> requestParams, Map<String, Object> requestAttribute, OperateLog operateLog, LoginUser loginUser) {
        String operationObjectValue = "";
        try {
            if (StrUtil.isNotEmpty(operateLog.field())) {
                Object object = null;

                if (operateLog.dataSource() != null) {
                    if (DataSource.Front.equals(operateLog.dataSource())) {
                        object = requestParams;
                    } else if (DataSource.BeforeServer.equals(operateLog.dataSource())) {
                        object = beforeRes;
                    } else if (DataSource.AfterServer.equals(operateLog.dataSource())) {
                        object = afterRes;
                    } else if (DataSource.Return.equals(operateLog.dataSource())) {
                        object = resultRes;
                    } else if (DataSource.Attribute.equals(operateLog.dataSource())) {
                        object = requestAttribute;
                    }

                }

                //找到操作对象的取值对象
                if (object != null) {
                    List<String> resuList = new ArrayList<>();
                    List<String> fields = Arrays.asList(operateLog.field().split(","));
                    String jsonStr = JSON.toJSONString(object);
                    if (object instanceof List) {
                        for (Object obj : JSON.parseArray(jsonStr)) {
                            String itemJsonStr = JSON.toJSONString(obj);
                            if (obj instanceof Map) {
                                Map<String, Object> map = JSON.parseObject(itemJsonStr, Map.class);
                                for (String field : fields) {
                                    Object val = map.get(field);
                                    if (val != null) {
                                        resuList.add(val.toString());
                                    }
                                }
                            }
                        }
                    } else {
                        Map<String, Object> map = JSON.parseObject(jsonStr, Map.class);
                        //先找第一层数据
                        for (String field : fields) {
                            Object val = map.get(field);
                            if (val != null) {
                                resuList.add(val.toString());
                            }
                        }
                        //第一层找到后就不找第二层了
                        if (resuList.size() == 0) {
                            //找第二层
                            for (Map.Entry<String, Object> entry : map.entrySet()) {
                                Object objectItem = entry.getValue();
                                String jsonStrItem = JSON.toJSONString(objectItem);
                                if (objectItem instanceof List) {
                                    for (Object obj : JSON.parseArray(jsonStrItem)) {
                                        if (obj instanceof Map) {
                                            String itemJsonStr = JSON.toJSONString(obj);
                                            Map<String, Object> mapItem = JSON.parseObject(itemJsonStr, Map.class);
                                            for (String field : fields) {
                                                Object val = mapItem.get(field);
                                                if (val != null) {
                                                    resuList.add(val.toString());
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    Map<String, Object> mapItem = JSON.parseObject(jsonStr, Map.class);
                                    for (String field : fields) {
                                        Object val = mapItem.get(field);
                                        if (val != null) {
                                            resuList.add(val.toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (resuList.size() > 0) {
                        operationObjectValue = String.join(",", resuList);
                        operationObjectValue = operationObjectValue.replaceAll("\\[", "").replaceAll("\\]", "");
                    }
                }
            }


        } catch (Exception e) {
            operationObjectValue = "解析失败";
            log.info("日志保存错误{}", e.getMessage(), e);
        } finally {
            //记录日志数据
            this.saveLog(requestParams, loginUser, operationObjectValue, operateLog);
        }
    }


    /**
     * 持久化操作日志
     */
    private void saveLog(Map<String, Object> requestParams, LoginUser loginUser, String operationObjectValue, OperateLog operateLog) {
        requestParams.remove("response");
        OperateLogEnum.Content content = operateLog.name();
        OperateLogReq logReq = new OperateLogReq();
        logReq.setOperateModule(content.getModule().getCode());
        logReq.setOperateType(operateLog.type().getType());
        logReq.setOperationContent(content.getCode());
        logReq.setOperateUserId(loginUser.getUser().getUserId().toString());
        logReq.setOperateUserName(loginUser.getUser().getNickName());
        logReq.setCreateTime(LocalDateTime.now());
        logReq.setIpAddress(requestParams.get("ip") == null ? "" : requestParams.get("ip").toString());
        Platform platform = requestParams.get("platform") == null ? null : Platform.getByCode(requestParams.get("platform").toString());
        logReq.setSource(platform == null ? "未知来源" : platform.getValue());
        logReq.setUri(requestParams.get("uri") == null ? "" : requestParams.get("uri").toString());
        logReq.setParam(JSON.toJSONString(requestParams));
        logReq.setDescription(operationObjectValue);
        //发送消息
        operateLogService.saveOperateLog(logReq);
    }

    /**
     * 有条件获取方法处理前数据
     *
     * @return
     */
    private Object getBeforeResByCondition(OperateLog operateLog, Map<String, Object> requestParams, LoginUser loginUser) {
        Object beforeRes = null;
        //数据来源为空或者反射方法为空时直接返回
        if (operateLog.dataSource() == null || operateLog.operationObjectClass() == null) {
            return beforeRes;
        }
        try {
            //处理前查询数据
            if (DataSource.BeforeServer.equals(operateLog.dataSource())) {
                beforeRes = this.getOperateObjectFromServer(loginUser, operateLog, requestParams);
            }
        } catch (Exception e) {
            log.info("日志反射错误{}", e.getMessage(), e);
        }
        return beforeRes;
    }

    /**
     * 有条件获取方法处理后数据
     *
     * @return
     */
    private Object getAfterResByCondition(OperateLog operateLog, Map<String, Object> requestParams, LoginUser loginUser) {
        Object afterRes = null;
        //数据来源为空或者反射方法为空时直接返回
        if (operateLog.dataSource() == null || operateLog.operationObjectClass() == null) {
            return afterRes;
        }
        try {
            //处理前查询数据
            if (DataSource.AfterServer.equals(operateLog.dataSource())) {
                afterRes = this.getOperateObjectFromServer(loginUser, operateLog, requestParams);
            }
        } catch (Exception e) {
            log.info("日志反射错误{}", e.getMessage(), e);
        }
        return afterRes;
    }

    /**
     * 从server中获取操作对象
     *
     * @param loginUser
     * @param operateLog
     * @param requestParams
     * @return
     * @throws Exception
     */
    private Object getOperateObjectFromServer(LoginUser loginUser, OperateLog operateLog, Map<String, Object> requestParams) throws Exception {
        //获取反射类
        if (StrUtil.isEmpty(operateLog.operationObjectClass())) {
            log.error("日志数据来源配置错误,反射方法名为空或者反射类名为空");
            return null;
        }

        try {
            Object object = SpringUtil.getBean(operateLog.operationObjectClass());
            Method method = object.getClass().getDeclaredMethod("getOperateObject", OperateLogEnum.Content.class, Map.class, LoginUser.class);
            method.setAccessible(true);
            //反射方法获取结果
            return method.invoke(object, operateLog.name(), requestParams, loginUser);
        } catch (Exception e) {
            log.error("日志通过反射方法获取操作对象数据时候错误，{}", e.getMessage());
        }
        return null;
    }

    /**
     * 参数解析
     */
    private Map<String, Object> requestAttributeResolver(HttpServletRequest request) {
        Map<String, Object> attributeMap = new HashMap<>();
        Enumeration<String> attributeNames = request.getAttributeNames();

        while (attributeNames.hasMoreElements()) {
            String attributeName = attributeNames.nextElement();
            // 排除内置属性（如 Tomcat、Servlet 规范相关的属性）
            if (isBuiltInAttribute(attributeName)) {
                continue; // 跳过内置属性
            }
            attributeMap.put(attributeName, request.getAttribute(attributeName));
        }
        return attributeMap;
    }

    // 判断是否是内置属性（可根据实际需求调整）
    private boolean isBuiltInAttribute(String attributeName) {
        return attributeName.startsWith("javax.servlet.")
                || attributeName.startsWith("org.apache.")
                || attributeName.startsWith("java.")
                || attributeName.startsWith("com.sun.")
                || attributeName.startsWith("org.spring")
                || attributeName.startsWith("__spring");
    }

    /**
     * 参数解析
     */
    private Map<String, Object> requestArgumentResolver(ProceedingJoinPoint joinPoint, HttpServletRequest request) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return this.parseJParameters(joinPoint, request);
    }

    private Map<String, Object> parseJParameters(ProceedingJoinPoint joinPoint, HttpServletRequest request) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        // 请求参数处理
        Object[] args = joinPoint.getArgs();
        final Map<String, Object> paraMap = new HashMap<>(16);
        if (request.getContentType().equals(ContentType.JSON.getValue()) ||
                request.getContentType().equals(ContentType.FORM_URLENCODED.getValue())) {
            for (int i = 0; i < args.length; i++) {
                // 读取方法参数
                MethodParameter methodParam = new SynthesizingMethodParameter(method, i);
                methodParam.initParameterNameDiscovery(PARAMETER_NAME_DISCOVERER);
                // PathVariable 参数跳过
                PathVariable pathVariable = methodParam.getParameterAnnotation(PathVariable.class);
                if (pathVariable != null) {
                    continue;
                }
                RequestBody requestBody = methodParam.getParameterAnnotation(RequestBody.class);
                String parameterName = methodParam.getParameterName();
                Object value = args[i];
                // 如果是body的json则是对象
                if (requestBody != null && value != null && value instanceof List == false) {
                    paraMap.putAll(BeanUtil.beanToMap(value));
                    continue;
                }

                // 处理 参数
                if (value instanceof HttpServletRequest) {
                    paraMap.putAll(((HttpServletRequest) value).getParameterMap());
                } else if (value instanceof WebRequest) {
                    paraMap.putAll(((WebRequest) value).getParameterMap());
                } else {
                    // 参数名
                    RequestParam requestParam = methodParam.getParameterAnnotation(RequestParam.class);
                    String paraName;
                    if (requestParam != null && StrUtil.isNotBlank(requestParam.value())) {
                        paraName = requestParam.value();
                    } else {
                        paraName = methodParam.getParameterName();
                    }
                    paraMap.put(paraName, value);
                }
            }
        }

        //获取IP地址
        paraMap.put("ip", this.getClientIp(request));

        //获取请求的uri
        paraMap.put("uri", this.getFullRequestUri(request));

        //添加来源
        paraMap.put("platform", request.getHeader("platform"));

        return paraMap;
    }


    /**
     * 获取登录用户信息
     *
     * @param request
     * @return
     */
    private LoginUser getLoginUser(HttpServletRequest request) {
        String userKey = request.getHeader("login_user_key");
        LoginUser loginUser = new LoginUser();
        LoginUser.SysUser user = new LoginUser.SysUser();
        user.setUserId(-1L);
        user.setNickName("system");
        loginUser.setUser(user);
        if (StrUtil.isBlank(userKey)) {
            return loginUser;
        }
        String obj = (String) redisCache.getCacheObject(LOGIN_TOKENS + userKey);
        if (StrUtil.isBlank(obj)) {
            return loginUser;
        }
        return JSON.parseObject(obj, LoginUser.class);
    }

    /**
     * 获取客户端IP地址
     *
     * @param request
     * @return
     */
    public String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For"); // 优先检查 XFF
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP"); // 检查 X-Real-IP（Nginx）
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr(); // 回退到直接 IP
        }

        // 处理多层代理的情况（X-Forwarded-For 可能包含多个 IP，如 "client, proxy1, proxy2"）
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim(); // 取第一个 IP
        }

        return ip;
    }

    /**
     * 获取请求的uri
     *
     * @param request
     * @return
     */
    public String getFullRequestUri(HttpServletRequest request) {
        String uri = request.getRequestURI(); // 获取路径部分，如 `/app/user`
        String queryString = request.getQueryString(); // 获取查询参数，如 `id=123&name=test`

        if (queryString != null) {
            uri += "?" + queryString; // 拼接成完整 URI，如 `/app/user?id=123&name=test`
        }

        return uri;
    }
}
