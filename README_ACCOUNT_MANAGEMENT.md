# 账号管理功能实现说明

## 功能概述

基于现有用户体系，新增了账号管理功能，包含以下特性：

### 新增字段
- `customer_id`: 客户ID（预留字段，可查询）
- `customer_name`: 客户名称（预留字段，可查询）
- `carrier_status`: 承运开通状态（enabled/disabled）

### 查询功能
- 支持手机号模糊查询
- 支持昵称模糊查询
- 支持客户ID精确查询
- 支持账号状态筛选
- 支持承运开通状态筛选

## API接口

### 1. 账号列表接口
```
POST /admin/rpc/account/list
```

**请求参数 (AccountQueryDTO):**
```json
{
  "phonenumber": "138",      // 手机号模糊查询
  "nickName": "张",          // 昵称模糊查询
  "customerId": "C001",      // 客户ID
  "status": "0",             // 账号状态
  "carrierStatus": "enabled" // 承运开通状态
}
```

**响应数据 (AccountVO):**
```json
{
  "code": 200,
  "data": [
    {
      "userId": 1,
      "createBy": "admin",
      "createTime": "2024-01-01 10:00:00",
      "phonenumber": "***********",
      "nickName": "张三",
      "customerName": "客户A",
      "customerId": "C001",
      "status": "0",
      "carrierStatus": "enabled"
    }
  ]
}
```

### 2. 新增账号接口
```
POST /admin/rpc/account/add
```

**请求参数 (AccountCreateDTO):**
```json
{
  "nickName": "李四",
  "phonenumber": "***********",
  "customerId": "C002",
  "carrierStatus": "enabled",
  "createBy": "admin"
}
```

**响应数据:**
```json
{
  "code": 200,
  "data": "账号创建成功"
}
```

## 业务逻辑

### 新增账号逻辑
1. 检查手机号是否已存在
2. 如果存在且已关联客户：提示"该手机账号已关联xxxxx，如需更换关联企业，在【账号管理】的详情页，更改客户即可。"
3. 如果存在但未关联客户：只做客户账号关联
4. 如果不存在：创建新用户，状态设为"inactive"（未激活）

### 状态说明
- 账号状态：0=正常，1=停用，inactive=未激活
- 承运开通状态：enabled=启用，disabled=禁用

## 数据库变更

执行以下SQL脚本：
```sql
-- 添加新字段
ALTER TABLE sys_user ADD COLUMN customer_id VARCHAR(50) COMMENT '客户ID（预留字段）';
ALTER TABLE sys_user ADD COLUMN customer_name VARCHAR(100) COMMENT '客户名称（预留字段）';
ALTER TABLE sys_user ADD COLUMN carrier_status VARCHAR(20) DEFAULT 'disabled' COMMENT '承运开通状态（enabled/disabled）';

-- 添加索引
CREATE INDEX idx_sys_user_customer_id ON sys_user(customer_id);
CREATE INDEX idx_sys_user_phonenumber ON sys_user(phonenumber);
CREATE INDEX idx_sys_user_nick_name ON sys_user(nick_name);
CREATE INDEX idx_sys_user_carrier_status ON sys_user(carrier_status);
```

## 文件清单

### API层
- `AdminAccountFeign.java` - Feign接口定义
- `AccountQueryDTO.java` - 查询参数DTO
- `AccountCreateDTO.java` - 创建参数DTO
- `AccountVO.java` - 响应数据VO

### 数据层
- `SysUserMapper.java` - 添加账号管理相关方法
- `SysUserMapper.xml` - 添加SQL映射
- `SysUser.java` - 添加新字段

### 服务层
- `IAccountService.java` - 账号管理服务接口
- `AccountServiceImpl.java` - 账号管理服务实现

### 控制层
- `AdminAccountFeignImpl.java` - 账号管理控制器

### 数据库
- `add_account_management_fields.sql` - 数据库变更脚本

## 使用示例

```java
// 查询账号列表
AccountQueryDTO queryDTO = AccountQueryDTO.builder()
    .phonenumber("138")
    .nickName("张")
    .build();
R<List<AccountVO>> result = adminAccountFeign.list(queryDTO);

// 新增账号
AccountCreateDTO createDTO = AccountCreateDTO.builder()
    .nickName("李四")
    .phonenumber("***********")
    .customerId("C002")
    .carrierStatus("enabled")
    .createBy("admin")
    .build();
R<String> result = adminAccountFeign.add(createDTO);
```