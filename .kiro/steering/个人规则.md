---
inclusion: always
---

# 项目开发规范

## 语言规范
- 所有交流和文档使用中文
- 代码注释使用中文
- 变量和方法命名使用英文，但注释说明使用中文

## 项目架构规范
- 遵循分层架构：facade（控制层）-> server（业务层）-> dal（数据访问层）
- API模块定义接口契约和数据传输对象
- Common模块提供通用工具类和配置

## 代码规范
- 使用Spring Boot框架开发
- 遵循RESTful API设计原则
- 使用MyBatis进行数据库操作
- 统一异常处理和返回格式
- 使用AjaxResult或R类作为统一响应格式
- **不要格式化代码** - 保持现有代码格式不变

## 命名规范
- Controller类以Controller结尾
- Service接口以I开头，实现类以Impl结尾
- DTO类以DTO结尾，VO类以VO结尾
- Mapper接口以Mapper结尾
- 数据库表名使用下划线命名法


## 日志规范
- 重要业务操作需要记录审计日志
- 异常信息需要完整记录便于排查