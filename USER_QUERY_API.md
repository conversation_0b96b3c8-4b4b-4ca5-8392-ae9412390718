# 用户查询API文档

## 概述
新增了系统用户查询功能，提供灵活的用户信息查询接口。

## 接口信息

### 查询用户列表
- **URL**: `/system/userQuery/list`
- **方法**: GET
- **权限**: `system:user:list`

### 查询参数 (SysUserQueryDTO)
| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| status | String | 用户状态 | "0"(正常), "1"(停用) |
| createTimeStart | LocalDateTime | 注册时间开始 | 2024-01-01T00:00:00 |
| createTimeEnd | LocalDateTime | 注册时间结束 | 2024-12-31T23:59:59 |
| platform | String | 注册来源 | "wechat", "app" |
| userId | Long | 用户ID | 123456 |
| phonenumber | String | 手机号(模糊查询) | "138" |
| nickName | String | 昵称(模糊查询) | "张三" |
| userType | String | 是否为企业用户 | "B"(企业), "C"(个人) |

### 响应字段 (SysUserQueryVO)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| userId | Long | 用户ID |
| platform | String | 注册来源 |
| phonenumber | String | 手机号 |
| nickName | String | 昵称 |
| createTime | LocalDateTime | 注册时间 |
| status | String | 用户状态 |
| authStatus | String | 用户认证状态 |
| userType | String | 是否为企业用户 |
| customerName | String | 企业名称 |
| carrierStatus | String | 企业账号状态 |
| orderCount | Long | 历史订单数量 |

## 使用示例

```bash
# 查询所有正常状态的用户
GET /system/userQuery/list?status=0

# 查询指定时间范围内注册的用户
GET /system/userQuery/list?createTimeStart=2024-01-01T00:00:00&createTimeEnd=2024-01-31T23:59:59

# 查询微信小程序注册的企业用户
GET /system/userQuery/list?platform=wechat&userType=B

# 根据手机号模糊查询
GET /system/userQuery/list?phonenumber=138
```

## 字段说明

### authStatus (用户认证状态)
- `inactive`: 未激活
- `unverified`: 未实名
- `verified`: 已实名
- `cancelled`: 已注销

## 注意事项
1. 支持分页查询，使用标准的分页参数
2. 历史订单数量字段目前返回0，需要后续关联订单表实现
3. 查询条件支持组合使用
4. 所有查询都会自动过滤已删除的用户(del_flag != '2')
5. authStatus字段新增，用于标识用户的认证状态