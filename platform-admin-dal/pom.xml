<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>platform-admin</artifactId>
        <groupId>com.yuanshuo</groupId>
        <version>3.8.9</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>platform-admin-dal</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>

        <!-- API模块 -->
        <dependency>
            <groupId>com.yuanshuo</groupId>
            <artifactId>platform-admin-api</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.yuanshuo</groupId>
            <artifactId>platform-admin-common</artifactId>
        </dependency>

        <!-- 获取系统信息 -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuanshuo</groupId>
            <artifactId>platform-order-api</artifactId>
            <version>1.0.11-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>