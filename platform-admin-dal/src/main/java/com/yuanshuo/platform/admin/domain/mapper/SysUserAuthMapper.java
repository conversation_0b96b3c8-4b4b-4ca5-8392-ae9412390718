package com.yuanshuo.platform.admin.domain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户登录方式表mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SysUserAuthMapper extends BaseMapper<SysUserAuth> {

    /**
     * 新增
     * @param userAuth
     * @return
     */
    public int insertUserAuth(SysUserAuth userAuth);

    /**
     * 更新
     */
    public int updateUserAuth(SysUserAuth userAuth);


    /**
     * 根据用户id,认证类型,标识 查询用户登录方式
     * @param userId 用户id
     * @param authType 认证类型
     * @param identifier 标识
     * @return
     */
    public List<SysUserAuth> selectUserAuthBy(@Param("userId") Long userId, @Param("authType") String authType, @Param("identifier") String identifier);

    /**
     * 根据用户id删除用户登录方式
     * @param userId
     * @return
     */
    int deleteUserAuthByUserId(Long userId);

    /**
     * 批量新增
     */
    int batchInsertUserAuth(List<SysUserAuth> list);
}
