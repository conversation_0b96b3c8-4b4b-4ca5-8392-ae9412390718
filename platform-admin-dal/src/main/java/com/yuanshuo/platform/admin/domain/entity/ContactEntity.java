package com.yuanshuo.platform.admin.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 常用联系人实体类
 */
@Data
@TableName("contact")
public class ContactEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 联系人手机号
     */
    private String phone;
    
    /**
     * 关联的用户ID
     */
    private Long userId;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}