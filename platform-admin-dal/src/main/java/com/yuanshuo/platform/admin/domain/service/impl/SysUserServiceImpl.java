package com.yuanshuo.platform.admin.domain.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysDept;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth;
import com.yuanshuo.platform.admin.common.core.domain.excel.SysUserImportData;
import com.yuanshuo.platform.admin.common.core.domain.model.req.SysUserAuthReq;
import com.yuanshuo.platform.admin.api.enums.AuthType;
import com.yuanshuo.platform.admin.api.enums.Platform;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.common.exception.ServerErrorCode;
import com.yuanshuo.platform.admin.common.qywx.service.WxCpHelper;
import com.yuanshuo.platform.admin.common.utils.TransferUtils;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.service.ISysUserAuthService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.yuanshuo.platform.admin.common.annotation.DataScope;
import com.yuanshuo.platform.admin.common.constant.UserConstants;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysRole;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.exception.ServiceException;
import com.yuanshuo.platform.admin.common.utils.SecurityUtils;
import com.yuanshuo.platform.admin.common.utils.StringUtils;
import com.yuanshuo.platform.admin.common.utils.bean.BeanValidators;
import com.yuanshuo.platform.admin.common.utils.spring.SpringUtils;
import com.yuanshuo.platform.admin.domain.entity.SysPost;
import com.yuanshuo.platform.admin.domain.entity.SysUserPost;
import com.yuanshuo.platform.admin.domain.entity.SysUserRole;
import com.yuanshuo.platform.admin.domain.mapper.SysPostMapper;
import com.yuanshuo.platform.admin.domain.mapper.SysRoleMapper;
import com.yuanshuo.platform.admin.domain.mapper.SysUserMapper;
import com.yuanshuo.platform.admin.domain.mapper.SysUserPostMapper;
import com.yuanshuo.platform.admin.domain.mapper.SysUserRoleMapper;
import com.yuanshuo.platform.admin.domain.service.ISysConfigService;
import com.yuanshuo.platform.admin.domain.service.ISysDeptService;
import com.yuanshuo.platform.admin.domain.service.ISysUserService;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    protected Validator validator;

    @Autowired
    private ISysUserAuthService sysUserAuthService;

    @Autowired
    private WxCpHelper wxCpHelper;

    @Autowired
    private SysUserNewRepository sysUserService;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        List<SysUser> sysUsers = userMapper.selectUserList(user);
        return sysUsers;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    @Override
    public SysUser selectUserByOpenId(String openId) {
        return userMapper.selectUserByOpenId(openId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }
    @Autowired
    private ISysUserService userService;
    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {

        Long userId1 = SecurityUtils.getUserId();
        SysUser sysUser = userService.selectUserById(userId1);
        if (!SecurityUtils.isAdmin(sysUser)) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);

        return rows;
    }

    // 新增用户与登录方式管理
    private void insertUserAuthLogin(SysUser user) {
        List<SysUserAuthReq> userAuths = user.getUserAuths();
        if (CollUtil.isNotEmpty(userAuths)) {
            List<SysUserAuth> transfers = TransferUtils.transfers(userAuths, SysUserAuth::new, (form, to) -> {
                to.setUserId(user.getUserId());
                // 如果是企业微信 且 登录标识为空 且 手机号不为空 根据手机号获取企业微信的userId
                if (StrUtil.equals(form.getAuthType(), AuthType.WeCom.name())
                        && StrUtil.isEmpty(form.getIdentifier())
                        && StrUtil.isNotEmpty(user.getPhonenumber())
                ) {
                    WxCpService wxCpService = wxCpHelper.getCpService();
                    try {
                        // 根据手机号获取企业微信的userId
                        String qywxUserId = wxCpService.getUserService().getUserId(user.getPhonenumber());
                        to.setIdentifier(qywxUserId);

                        // 查询企业微信是否存在
                        SysUserAuth one = sysUserAuthService.getOne(Wrappers.lambdaQuery(SysUserAuth.class).eq(SysUserAuth::getIdentifier, qywxUserId).last("LIMIT 1"));
                        if (one != null) {
                            throw ServerErrorCode.ERROR.exp("企业微信账号已存在");
                        }
                    } catch (WxErrorException e) {
                        throw ServerErrorCode.ERROR.exp("企业微信授权失败");
                    }
                }
            });
            sysUserAuthService.batchInsertUserAuth(transfers);
        }
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        // 删除用户登录方式
        sysUserAuthService.deleteUserAuthByUserId(userId);
        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    deptService.checkDeptDataScope(user.getDeptId());
                    String password = configService.selectConfigByKey("sys.user.initPassword");
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    userMapper.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getUserId());
                    deptService.checkDeptDataScope(user.getDeptId());
                    user.setUserId(u.getUserId());
                    user.setUpdateBy(operName);
                    userMapper.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public SysUser selectUserByPhoneNumber(String phoneNumber) {
        return userMapper.selectUserByPhoneNumber(phoneNumber);
    }

    @Override
    public SysUser selectUserByQywxUserId(String weChatUserId) {
        return userMapper.selectUserByQywxUserId(weChatUserId);
    }

    @Override
    public String importTemplate(List<SysUserImportData> userList, String operName) {

        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 获取所有企业微信系统用户
        List<SysUserAuth> sysUserAuths = sysUserAuthService.selectUserAuthListByAuthType(AuthType.WeCom.getCode());
        // 获取所有部门
        List<SysDept> sysDepts = deptService.selectDeptList(new SysDept());

        for (SysUserImportData user : userList) {
            try {
                // 检查当前导入的用户是否已存在于系统中（通过用户名判断）
                boolean exists = sysUserAuths.stream()
                        .anyMatch(sysUserAuth -> sysUserAuth.getIdentifier().equals(user.getUserName()));

                if (!exists) {
                    // 先处理部门名称,  获取部门ID
                    Long deptId = processDeptNames(user.getDeptName(), sysDepts);
                    // 用户不存在，执行新增操作
                    // 新增 SysUser 表

                    SysUserEntity newUser = new SysUserEntity();
                    newUser.setUserName(user.getUserName());
                    newUser.setNickName(user.getNickName());
                    newUser.setPassword(SecurityUtils.encryptPassword("123456")); // 设置默认密码
                    newUser.setPhonenumber(user.getPhonenumber());
                    newUser.setEmail(user.getEmail());
                    newUser.setDeptId(deptId); // 需要确保部门存在
                    newUser.setCreateBy(operName);
                    newUser.setUserType(UserType.Internal.getCode());
                    newUser.setSex(user.getSex());
                    newUser.setPlatform(Platform.SYSTEM.getCode());
                    newUser.setCreateTime(LocalDateTime.now());
                    newUser.setUpdateTime(LocalDateTime.now());
                    newUser.setRemark("通讯录导入");
                    newUser.setStatus(StrUtil.equals("已激活", user.getActiveState()) ? "0" : "1");
                    sysUserService.save(newUser);

                    // 新增 SysUserAuth 表
                    SysUserAuth newUserAuth = new SysUserAuth();
                    newUserAuth.setUserId(newUser.getUserId());
                    newUserAuth.setAuthType(AuthType.WeCom.getCode()); // 企业微信类型
                    newUserAuth.setIdentifier(user.getUserName()); // 假设 SysUserImportData 中有 weChatUserId 字段
                    newUserAuth.setStatus(1);
                    sysUserAuthService.save(newUserAuth);

                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else {
                    successNum++;
                    successMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    public Long processDeptNames(String deptName, List<SysDept> sysDepts) {
        String[] deptPaths = deptName.split(";");
        Long currentParentId = 0L;

        for (String deptPath : deptPaths) {
            String[] deptLevels = deptPath.split("/");
            currentParentId = 0L;

            for (String deptLevel : deptLevels) {
                SysDept dept = findOrCreateDept(deptLevel, currentParentId, sysDepts);
                currentParentId = dept.getDeptId();
            }
        }
        return currentParentId;
    }

    private SysDept findOrCreateDept(String deptName, Long parentId, List<SysDept> sysDepts) {
        // 根据deptName和parentId查询部门 查询在sysDepts中是否存在
        SysDept dept = sysDepts.stream().filter(sysDept -> sysDept.getDeptName().equals(deptName) && sysDept.getParentId().equals(parentId)).findFirst().orElse(null);
        if (dept == null) {
            dept = new SysDept();
            dept.setDeptName(deptName);
            dept.setParentId(parentId);
            dept.setStatus(UserConstants.DEPT_NORMAL);
            deptService.insertDept(dept);
            sysDepts.add(dept);
        }
        return dept;
    }

    @Override
    public List<SysUser> selectUsersByIdsAndType(List<Long> ids, String userType) {
        return userMapper.selectUsersByIdsAndType(ids, userType);
    }


    @Override
    public void updateUserSalesperson(SysUser dbUser) {
        userMapper.updateUserSalesperson(dbUser);
    }

    @Override
    public void insertUserSalesperson(SysUser sysUser) {
        userMapper.insertUserSalesperson(sysUser);
    }

    @Override
    public List<SysUser> selectNewUserList(List<Long> userIdList, String nickNameLike, Integer isSalesperson) {
        return userMapper.selectNewUserList(userIdList,nickNameLike,isSalesperson);
    }
}
