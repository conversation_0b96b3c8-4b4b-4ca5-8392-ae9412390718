package com.yuanshuo.platform.admin.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("customer")
public class Customer {

    /**
     * 自增主键（表内部唯一标识）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户ID（业务唯一标识，按特殊格式生成）
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 客户全称（1-20字，中英文，平台唯一）
     */
    @TableField("customer_full_name")
    private String customerFullName;

    /**
     * 状态：1-启用，0-禁用（禁用时客户下所有账号不可下承运订单）
     */
    @TableField("status")
    private Integer status;

    /**
     * 客户性质：1-企业，0-个人（默认企业）
     */
    @TableField("customer_type")
    private Integer customerType;

    /**
     * 行业：1-快递，2-生鲜，3-医药，4-商超零售，5-布草，6-餐具，7-汽配，8-其他（默认快递）
     */
    @TableField("industry")
    private Integer industry;

    /**
     * 社会统一信用代码（18位数字+大写字母，企业性质时必填）
     */
    @TableField("unified_social_credit_code")
    private String unifiedSocialCreditCode;

    /**
     * 联系方式（手机号，校验格式）
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 城市（城市-省市级联：存储城市名称）
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 详细地址（对接高德API，存储具体地址文本）
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 月结账号（系统生成，平台唯一，客户维度）
     */
    @TableField("monthly_account")
    private String monthlyAccount;

    /**
     * 分配点位（存储点位ID列表，可用逗号分隔）
     */
    @TableField("station_ids")
    private String stationIds;

    /**
     * 创建人id（系统生成）
     */
    @TableField(value = "create_user")
    private String createUser;


    /**
     * 创建人姓名
     */
    @TableField(value = "create_name")
    private String createName;

    /**
     * 创建时间（系统生成）
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间（自动更新）
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    @TableField("is_delete")
    private Integer isDelete;



}
