package com.yuanshuo.platform.admin.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanshuo.platform.admin.domain.entity.Customer;
import com.yuanshuo.platform.admin.domain.mapper.CustomerMapper;
import com.yuanshuo.platform.admin.domain.service.IContractService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 合同服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class ContractServiceImpl implements IContractService {
    
    @Resource
    private CustomerMapper customerMapper;
    
    @Override
    public boolean isActiveContractContactPhone(String phoneNumber) {
        // 参数校验
        if (!StringUtils.hasText(phoneNumber)) {
            return false;
        }
        
        // 查询customer表中status=1且contact_phone等于指定手机号的记录
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getStatus, 1)
                   .eq(Customer::getContactPhone, phoneNumber.trim())
                   .eq(Customer::getIsDelete, 0);

        Long l = customerMapper.selectCount(queryWrapper);
        return l>0;
    }
    
    @Override
    public List<String> getCustomerNameByPhone(String phoneNumber) {
        // 参数校验
        if (!StringUtils.hasText(phoneNumber)) {
            return new ArrayList<>();
        }
        
        // 查询customer表中status=1且contact_phone等于指定手机号的记录
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getStatus, 1)
                   .eq(Customer::getContactPhone, phoneNumber.trim())
                   .eq(Customer::getIsDelete, 0);
        
        List<Customer> customers = customerMapper.selectList(queryWrapper);
        return customers.stream()
                .map(Customer::getCustomerFullName)
                .collect(Collectors.toList());
    }
}