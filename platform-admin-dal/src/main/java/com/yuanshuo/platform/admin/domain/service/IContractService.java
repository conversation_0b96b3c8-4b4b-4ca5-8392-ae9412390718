package com.yuanshuo.platform.admin.domain.service;

import java.util.List;

/**
 * 合同服务接口
 * 
 * <AUTHOR>
 */
public interface IContractService {
    
    /**
     * 检查手机号是否为启用状态的合同联系手机号
     * 
     * @param phoneNumber 手机号
     * @return true-是启用的合同联系手机号，false-不是
     */
    boolean isActiveContractContactPhone(String phoneNumber);
    
    /**
     * 根据手机号获取客户名称列表
     * 
     * @param phoneNumber 手机号
     * @return 客户名称列表，如果没有找到则返回空列表
     */
    List<String> getCustomerNameByPhone(String phoneNumber);
}