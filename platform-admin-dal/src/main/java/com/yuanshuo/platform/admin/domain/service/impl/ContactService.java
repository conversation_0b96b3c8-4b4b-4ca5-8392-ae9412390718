package com.yuanshuo.platform.admin.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanshuo.common.entity.exception.BusinessException;
import com.yuanshuo.platform.admin.api.enums.Platform;
import com.yuanshuo.platform.admin.api.model.dto.ContactDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.ContactQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.ContactVO;
import com.yuanshuo.platform.admin.common.constant.Constants;
import com.yuanshuo.platform.admin.common.enums.UserAuthStatus;
import com.yuanshuo.platform.admin.common.enums.UserStatus;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.common.utils.SecurityUtils;
import com.yuanshuo.platform.admin.domain.entity.ContactEntity;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.mapper.ContactMapper;
import com.yuanshuo.platform.admin.domain.service.impl.SysUserNewRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 常用联系人 业务层实现
 */
@Service
public class ContactService extends ServiceImpl<ContactMapper, ContactEntity> {

    @Autowired
    private SysUserNewRepository sysUserService;

    /**
     * 查询联系人列表
     *
     * @param queryDTO 查询条件
     * @return 联系人列表
     */
    public List<ContactVO> selectContactList(ContactQueryDTO queryDTO) {
        LambdaQueryWrapper<ContactEntity> wrapper = new LambdaQueryWrapper<>();
        
        // 根据关键字模糊查询姓名或电话
        if (queryDTO != null && StringUtils.hasText(queryDTO.getKeyword())) {
            wrapper.and(w -> w.like(ContactEntity::getName, queryDTO.getKeyword())
                    .or().like(ContactEntity::getPhone, queryDTO.getKeyword()));
        }
        
        // 根据用户ID查询
        if (queryDTO != null && queryDTO.getUserId()!=null) {
            wrapper.eq(ContactEntity::getUserId, queryDTO.getUserId());
        }
        
        List<ContactEntity> entities = this.list(wrapper);
        List<ContactVO> voList = new ArrayList<>();
        for (ContactEntity entity : entities) {
            voList.add(convertToVO(entity));
        }
        return voList;
    }

    /**
     * 根据ID查询联系人
     *
     * @param id 联系人ID
     * @return 联系人信息
     */
    public ContactVO selectContactById(Long id) {
        ContactEntity entity = this.getById(id);
        return entity != null ? convertToVO(entity) : null;
    }

    /**
     * 新增联系人
     *
     * @param contactDTO 联系人信息
     * @return 结果
     */
    public Long insertContact(ContactDTO contactDTO) {
        // 检查联系人数量是否超过上限
        LambdaQueryWrapper<ContactEntity> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(ContactEntity::getUserId, contactDTO.getUserId());
        long currentCount = this.count(countWrapper);
        
        if (currentCount >= Constants.MAX_CONTACT_COUNT) {
            throw new BusinessException("已达到添加上限，请清除不常用联系人后继续添加");
        }
        
        // 检查是否已存在相同的userId+phone组合
        LambdaQueryWrapper<ContactEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContactEntity::getUserId, contactDTO.getUserId())
               .eq(ContactEntity::getPhone, contactDTO.getPhone());
        
        if (this.count(wrapper) > 0) {
            throw new BusinessException("已存在该信息，无需重复添加");
        }
        
        // 姓名为空时默认使用手机号后4位
        if (!StringUtils.hasText(contactDTO.getName()) && StringUtils.hasText(contactDTO.getPhone())) {
            String phone = contactDTO.getPhone();
            contactDTO.setName(phone.length() >= 4 ? phone.substring(phone.length() - 4) : phone);
        }
        
        // 检查手机号+usertype为C的账户是否已存在且未注销
        SysUserEntity existingUser = sysUserService.selectUserByPhoneAndUserType(contactDTO.getPhone(), UserType.C.getCode());
        if (existingUser == null) {
            // 不存在则创建未激活用户
            SysUserEntity newUser = createInactiveUser(contactDTO.getPhone(), contactDTO.getName());
            sysUserService.save(newUser);
        }
        
        ContactEntity entity = convertToEntity(contactDTO);
        this.save(entity);
        return entity.getId();
    }
    
    /**
     * 创建未激活用户
     */
    private SysUserEntity createInactiveUser(String phone, String name) {
        SysUserEntity user = new SysUserEntity();
        user.setUserName(phone);
        user.setNickName(StringUtils.hasText(name) ? name : "用户" + phone.substring(phone.length() - 4));
        user.setPhonenumber(phone);
        user.setPassword(SecurityUtils.encryptPassword(phone));
        user.setUserType(UserType.C.getCode());
        user.setStatus(UserStatus.OK.getCode());
        user.setAuthStatus(UserAuthStatus.INACTIVE.getCode());
        user.setPlatform(Platform.SYSTEM.getCode());
        user.setDelFlag("0");
        return user;
    }

    /**
     * 修改联系人
     *
     * @param contactDTO 联系人信息
     * @return 结果
     */
    public boolean updateContact(ContactDTO contactDTO) {
        // 检查是否已存在相同的userId+phone组合（排除当前记录）
        LambdaQueryWrapper<ContactEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContactEntity::getUserId, contactDTO.getUserId())
               .eq(ContactEntity::getPhone, contactDTO.getPhone())
               .ne(ContactEntity::getId, contactDTO.getId());
        
        if (this.count(wrapper) > 0) {
            throw new BusinessException("已存在该信息，无需重复添加");
        }
        
        // 姓名为空时默认使用手机号后4位
        if (!StringUtils.hasText(contactDTO.getName()) && StringUtils.hasText(contactDTO.getPhone())) {
            String phone = contactDTO.getPhone();
            contactDTO.setName(phone.length() >= 4 ? phone.substring(phone.length() - 4) : phone);
        }
        
        ContactEntity entity = convertToEntity(contactDTO);
        return this.updateById(entity);
    }

    /**
     * 删除联系人
     *
     * @param id 联系人ID
     * @return 结果
     */
    public boolean deleteContactById(Long id) {
        return this.removeById(id);
    }

    /**
     * 批量删除联系人
     *
     * @param ids 联系人ID数组
     * @return 结果
     */
    public boolean deleteContactByIds(Long[] ids) {
        return this.removeByIds(Arrays.asList(ids));
    }

    /**
     * 将Entity转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    private ContactVO convertToVO(ContactEntity entity) {
        ContactVO vo = new ContactVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 将DTO转换为Entity
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    private ContactEntity convertToEntity(ContactDTO dto) {
        ContactEntity entity = new ContactEntity();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
}