package com.yuanshuo.platform.admin.domain.service;

import com.yuanshuo.platform.admin.api.model.dto.AccountCreateDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.AccountQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.AccountVO;

import java.util.List;

/**
 * 账号管理服务接口
 */
public interface IAccountService {

    /**
     * 查询账号列表
     * 
     * @param queryDTO 查询条件
     * @return 账号列表
     */
    List<AccountVO> selectAccountList(AccountQueryDTO queryDTO);

    /**
     * 新增账号
     * 
     * @param createDTO 创建信息
     * @return 结果信息
     */
    String insertAccount(AccountCreateDTO createDTO);
}