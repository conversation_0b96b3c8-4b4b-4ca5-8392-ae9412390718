package com.yuanshuo.platform.admin.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.SysUserImportDTO;
import com.yuanshuo.platform.admin.api.model.dto.SysUserImportFailDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.SysUserQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.SysUserQueryVO;
import com.yuanshuo.platform.admin.api.enums.Platform;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.service.ISysUserQueryService;
import com.yuanshuo.platform.order.api.feign.TradeOrderFeign;
import com.yuanshuo.platform.order.api.model.dto.query.TradeOrderQueryPageDTO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 系统用户查询服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysUserQueryServiceImpl implements ISysUserQueryService {

    @Autowired
    private SysUserNewRepository sysUserNewRepository;
    @Autowired
    TradeOrderFeign tradeOrderFeign;

    @Override
    public PageInfo selectUserList(SysUserQueryDTO queryDTO) {
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (queryDTO.getStatus() != null) {
            wrapper.eq(SysUserEntity::getStatus, queryDTO.getStatus());
        }
        if (queryDTO.getCreateTimeStart() != null) {
            wrapper.ge(SysUserEntity::getCreateTime, queryDTO.getCreateTimeStart());
        }
        if (queryDTO.getCreateTimeEnd() != null) {
            wrapper.le(SysUserEntity::getCreateTime, queryDTO.getCreateTimeEnd());
        }
        if (queryDTO.getPlatform() != null) {
            wrapper.eq(SysUserEntity::getPlatform, queryDTO.getPlatform());
        }
        if (queryDTO.getUserIds() != null && !queryDTO.getUserIds().isEmpty()) {
            wrapper.in(SysUserEntity::getUserId, queryDTO.getUserIds());
        }
        if (queryDTO.getUserId() != null) {
            wrapper.like(SysUserEntity::getUserId, queryDTO.getUserId());
        }
        if (queryDTO.getPhonenumber() != null) {
            wrapper.like(SysUserEntity::getPhonenumber, queryDTO.getPhonenumber());
        }
        if (queryDTO.getNickName() != null) {
            wrapper.like(SysUserEntity::getNickName, queryDTO.getNickName());
        }
        if (queryDTO.getIsCorporate() != null) {
            if (queryDTO.getIsCorporate()) {
                wrapper.isNotNull(SysUserEntity::getCustomerId);
            } else {
                wrapper.isNull(SysUserEntity::getCustomerId);
            }
        }
        if (queryDTO.getIsWhitelist() != null) {
            wrapper.eq(SysUserEntity::getIsWhitelist, queryDTO.getIsWhitelist());
        }
        if (queryDTO.getAuthStatus() != null) {
            wrapper.eq(SysUserEntity::getAuthStatus, queryDTO.getAuthStatus());
        }
        wrapper.ne(SysUserEntity::getDelFlag, "2");
        wrapper.ne(SysUserEntity::getStatus, "2");
        wrapper.ne(SysUserEntity::getUserType, UserType.Internal.getCode());
        // 按创建时间倒序排序
        wrapper.orderByDesc(SysUserEntity::getCreateTime);

        List<SysUserEntity> userList = sysUserNewRepository.list(wrapper);
        PageInfo sysUserEntityPageInfo = new PageInfo<>(userList);
        List<SysUserQueryVO> collect = userList.stream().map(this::convertToVO).collect(Collectors.toList());
        PageInfo result = new PageInfo();
        result.setTotal(sysUserEntityPageInfo.getTotal());
        result.setList(collect);
        return result;
    }

    private SysUserQueryVO convertToVO(SysUserEntity entity) {
        TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
        tradeOrderQueryPageDTO.setPageNo(1);
        tradeOrderQueryPageDTO.setPageSize(1);
        tradeOrderQueryPageDTO.setUserId(entity.getUserId());
        tradeOrderQueryPageDTO.setParentType(true);
        R<TableDataInfo<TradeOrderVO>> tableDataInfoR = tradeOrderFeign.queryPageOrder(tradeOrderQueryPageDTO);
        SysUserQueryVO vo = new SysUserQueryVO();
        vo.setUserId(entity.getUserId())
                .setPlatform(Platform.getEnumByCode(entity.getPlatform()).getDesc())
                .setPhonenumber(entity.getPhonenumber())
                .setNickName(entity.getNickName())
                .setCreateTime(entity.getCreateTime() != null ? entity.getCreateTime()
                        .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null)
                .setStatus(entity.getStatus())
                .setCustomerName(entity.getCustomerName())
                .setOrderCount(tableDataInfoR.getData().getTotal())
                .setCouponCount(0l)
                .setIsWhitelist(entity.getIsWhitelist())
                .setInviterUserId(entity.getInviterUserId())
                .setInviterName(entity.getInviterName())
                .setAuthStatus(entity.getAuthStatus());
        if (entity.getCarrierStatus() != null) {
            vo.setCarrierStatus(entity.getCarrierStatus().equals("disabled") ? "禁用" : "启用");
        }
        if (entity.getCustomerId() == null) {
            vo.setIsCorporate(false);
        } else {
            vo.setIsCorporate(true);
        }
        return vo;
    }

    @Override
    public boolean setUserWhitelist(Long userId) {
        return sysUserNewRepository.lambdaUpdate()
                .eq(SysUserEntity::getUserId, userId)
                .set(SysUserEntity::getIsWhitelist, true)
                .update();
    }

    @Override
    public boolean removeUserWhitelist(Long userId) {
        return sysUserNewRepository.lambdaUpdate()
                .eq(SysUserEntity::getUserId, userId)
                .set(SysUserEntity::getIsWhitelist, false)
                .update();
    }

    @Override
    public List<SysUserImportFailDTO> batchImportUser(List<SysUserImportDTO> importList, String createBy) {
        List<SysUserImportFailDTO> failList = new ArrayList<>();
        Pattern phonePattern = Pattern.compile("^1\\d{10}$");

        for (SysUserImportDTO importDTO : importList) {
            SysUserImportFailDTO failDTO = new SysUserImportFailDTO()
                    .setPhonenumber(importDTO.getPhonenumber())
                    .setIsWhitelistStr(importDTO.getIsWhitelistStr());

            // 验证手机号格式
            if (!StringUtils.hasText(importDTO.getPhonenumber()) ||
                    !phonePattern.matcher(importDTO.getPhonenumber()).matches()) {
                failDTO.setFailReason("手机号格式错误");
                failList.add(failDTO);
                continue;
            }

            // 验证是否白名单字段
            Boolean isWhitelist = convertWhitelistStr(importDTO.getIsWhitelistStr());
            if (isWhitelist == null) {
                failDTO.setFailReason("是否白名单不符合规则");
                failList.add(failDTO);
                continue;
            }

            // 检查手机号是否已存在
            LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUserEntity::getPhonenumber, importDTO.getPhonenumber())
                    .ne(SysUserEntity::getAuthStatus, "cancelled");

            SysUserEntity existingUser = sysUserNewRepository.getOne(wrapper);

            try {
                if (existingUser != null) {
                    // 手机号已存在，只更新白名单字段
                    sysUserNewRepository.lambdaUpdate()
                            .eq(SysUserEntity::getUserId, existingUser.getUserId())
                            .set(SysUserEntity::getIsWhitelist, isWhitelist)
                            .update();
                } else {
                    // 创建新用户
                    SysUserEntity userEntity = new SysUserEntity();
                    userEntity.setPhonenumber(importDTO.getPhonenumber());
                    userEntity.setUserName(importDTO.getPhonenumber());
                    userEntity.setNickName(getLastFourDigits(importDTO.getPhonenumber()));
                    userEntity.setPlatform("excel_import");
                    userEntity.setAuthStatus("inactive"); // 未激活状态
                    userEntity.setStatus("0");
                    userEntity.setIsWhitelist(isWhitelist);
                    userEntity.setCreateTime(LocalDateTime.now());
                    userEntity.setCreateBy(createBy);
                    userEntity.setDelFlag("0");
                    userEntity.setUserType("C");

                    sysUserNewRepository.save(userEntity);
                }
            } catch (Exception e) {
                failDTO.setFailReason("系统异常：" + e.getMessage());
                failList.add(failDTO);
            }
        }

        return failList;
    }

    /**
     * 转换白名单字符串为布尔值
     */
    private Boolean convertWhitelistStr(String whitelistStr) {
        if (!StringUtils.hasText(whitelistStr)) {
            return null;
        }

        String str = whitelistStr.trim();
        if ("是".equals(str) || "true".equalsIgnoreCase(str) || "1".equals(str)) {
            return true;
        } else if ("否".equals(str) || "false".equalsIgnoreCase(str) || "0".equals(str)) {
            return false;
        }

        return null; // 不符合规则
    }

    /**
     * 获取手机号后4位作为昵称
     */
    private String getLastFourDigits(String phoneNumber) {
        if (StringUtils.hasText(phoneNumber) && phoneNumber.length() >= 4) {
            return phoneNumber.substring(phoneNumber.length() - 4);
        }
        return phoneNumber;
    }
}