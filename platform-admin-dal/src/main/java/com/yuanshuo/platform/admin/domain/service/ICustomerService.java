package com.yuanshuo.platform.admin.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanshuo.platform.admin.domain.entity.Customer;
import com.yuanshuo.platform.admin.domain.service.query.CustomerQuery;

import javax.validation.constraints.NotBlank;
import java.util.List;

public interface ICustomerService {

    int insert(Customer customer);

    IPage<Customer> selectPage(Page<Customer> page, LambdaQueryWrapper<Customer> queryWrapper);

    Customer selectByCustomerId(String customerId);

    void updateById(Customer customer);

    Customer selectCustomerByName(String customerFullName);

    List<Customer> queryCustomerList(CustomerQuery customerQuery);
}
