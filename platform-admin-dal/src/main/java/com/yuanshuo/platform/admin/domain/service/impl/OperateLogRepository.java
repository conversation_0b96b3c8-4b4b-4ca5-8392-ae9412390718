package com.yuanshuo.platform.admin.domain.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.platform.admin.domain.entity.OperateLogEntity;
import com.yuanshuo.platform.admin.domain.entity.dto.OperateLogQueryDTO;
import com.yuanshuo.platform.admin.domain.mapper.OperateLogMapper;
import org.springframework.stereotype.Repository;

@Repository
public class OperateLogRepository extends ServiceImpl<OperateLogMapper, OperateLogEntity> implements IService<OperateLogEntity> {
    public PageInfo<OperateLogEntity> pageList(OperateLogQueryDTO queryDTO) {
        LambdaQueryWrapper<OperateLogEntity> wrapper = new LambdaQueryWrapper<>();

        if (queryDTO.getCreateTimeStart() != null) {
            wrapper.ge(OperateLogEntity::getCreateTime, queryDTO.getCreateTimeStart());
        }
        if (queryDTO.getCreateTimeEnd() != null) {
            wrapper.le(OperateLogEntity::getCreateTime, queryDTO.getCreateTimeEnd());
        }
        if (StrUtil.isNotEmpty(queryDTO.getOperateContent())) {
            wrapper.eq(OperateLogEntity::getOperationContent, queryDTO.getOperateContent());
        }
        if (StrUtil.isNotEmpty(queryDTO.getModule())) {
            wrapper.eq(OperateLogEntity::getOperateModule, queryDTO.getModule());
        }
        if (StrUtil.isNotEmpty(queryDTO.getOperateUser())) {
            wrapper.like(OperateLogEntity::getOperateUserName, queryDTO.getOperateUser())
                    .or()
                    .like(OperateLogEntity::getOperateUserId, queryDTO.getOperateUser());
        }

        // 按创建时间倒序排序
        wrapper.orderByDesc(OperateLogEntity::getCreateTime);

        PageInfo pageInfo = new PageInfo<>(this.list(wrapper));
        return pageInfo;
    }

}
