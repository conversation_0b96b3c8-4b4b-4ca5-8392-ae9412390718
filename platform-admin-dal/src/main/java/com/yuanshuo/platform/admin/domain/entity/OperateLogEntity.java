package com.yuanshuo.platform.admin.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 常用联系人实体类
 */
@Data
@TableName("operate_log")
public class OperateLogEntity {
    /**
     * 操作人账号
     */
    private String operateUserId;

    /**
     * 操作人名称
     */
    private String operateUserName;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作来源(管理后台,App,小元智送小程序,小元车控小程序)
     */
    private String source;

    /**
     * 操作模块
     */
    private String operateModule;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 描述(操作对象ID)
     */
    private String description;

    /**
     * 操作参数
     */
    private String param;

    /**
     * 请求路径
     */
    private String uri;

    @TableId(
            type = IdType.AUTO
    )
    private Long id;
    @TableField("create_time")
    private LocalDateTime createTime;
    @TableField("update_time")
    private LocalDateTime updateTime;
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;
}