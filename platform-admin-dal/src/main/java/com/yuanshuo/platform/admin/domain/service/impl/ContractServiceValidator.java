package com.yuanshuo.platform.admin.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanshuo.platform.admin.domain.entity.Customer;
import org.springframework.util.StringUtils;

/**
 * 合同服务验证器 - 用于验证合同服务逻辑的正确性
 */
public class ContractServiceValidator {
    
    /**
     * 验证查询条件构建是否正确
     */
    public static void validateQueryConditions() {
        System.out.println("=== 验证合同服务查询条件构建 ===");
        
        // 测试参数校验
        String emptyPhone = "";
        String nullPhone = null;
        String validPhone = "13800138000";
        
        System.out.println("空字符串参数校验: " + !StringUtils.hasText(emptyPhone));
        System.out.println("null参数校验: " + !StringUtils.hasText(nullPhone));
        System.out.println("有效参数校验: " + StringUtils.hasText(validPhone));
        
        // 测试查询条件构建
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getStatus, 1)
                   .eq(Customer::getContactPhone, validPhone.trim())
                   .eq(Customer::getIsDelete, 0);
        
        System.out.println("查询条件构建成功");
        System.out.println("=== 验证完成 ===");
    }
    
    public static void main(String[] args) {
        validateQueryConditions();
    }
}