package com.yuanshuo.platform.admin.domain.service;

import com.github.pagehelper.PageInfo;
import com.yuanshuo.platform.admin.api.model.dto.SysUserImportDTO;
import com.yuanshuo.platform.admin.api.model.dto.SysUserImportFailDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.SysUserQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.SysUserQueryVO;

import java.util.List;

/**
 * 系统用户查询服务接口
 *
 * <AUTHOR>
 */
public interface ISysUserQueryService {

    /**
     * 查询用户列表
     *
     * @param queryDTO 查询条件
     * @return 用户列表
     */
    PageInfo selectUserList(SysUserQueryDTO queryDTO);

    /**
     * 设置用户为白名单
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean setUserWhitelist(Long userId);

    /**
     * 移除用户白名单
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean removeUserWhitelist(Long userId);

    /**
     * 批量导入用户
     *
     * @param importList 导入用户列表
     * @param createBy 创建人
     * @return 导入失败的用户列表
     */
    List<SysUserImportFailDTO> batchImportUser(List<SysUserImportDTO> importList, String createBy);
}