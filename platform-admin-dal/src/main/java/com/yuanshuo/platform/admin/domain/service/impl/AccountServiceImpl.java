package com.yuanshuo.platform.admin.domain.service.impl;

import com.github.pagehelper.PageInfo;
import com.yuanshuo.platform.admin.api.enums.Platform;
import com.yuanshuo.platform.admin.api.model.dto.AccountCreateDTO;
import com.yuanshuo.platform.admin.api.model.dto.AccountImportFailDTO;
import com.yuanshuo.platform.admin.api.model.dto.AccountUpdateDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.AccountQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.AccountVO;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUser;
import com.yuanshuo.platform.admin.common.enums.UserType;
import com.yuanshuo.platform.admin.common.exception.BusinessException;
import com.yuanshuo.platform.admin.domain.entity.Customer;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.mapper.SysUserMapper;
import com.yuanshuo.platform.admin.domain.service.IAccountService;
import com.yuanshuo.platform.admin.domain.service.ICustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yuanshuo.platform.admin.common.exception.ServerErrorCode.ERROR_2;

/**
 * 账号管理服务实现
 */
@Service
public class AccountServiceImpl {

    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private SysUserNewRepository sysUserService;
    @Autowired
    ICustomerService iCustomerService;

    public PageInfo<AccountVO> selectAccountList(AccountQueryDTO queryDTO) {
        List<SysUser> users = userMapper.selectAccountList(queryDTO);
        PageInfo pageInfo = new PageInfo();
        List<AccountVO> collect = users.stream().map(this::convertToAccountVO).collect(Collectors.toList());
        pageInfo.setList(collect);
        pageInfo.setTotal(new PageInfo<>(users).getTotal());
        return pageInfo;
    }

    public String insertAccount(AccountCreateDTO createDTO) {
        // 检查手机号是否已存在且用户类型为C
        SysUserEntity sysUserEntity = sysUserService.selectUserByPhoneAndUserType(createDTO.getPhonenumber(),
                UserType.C.getCode());

        if (sysUserEntity != null && "C".equals(sysUserEntity.getUserType())) {
            // 用户已存在，检查是否已关联客户
            if (StringUtils.hasText(sysUserEntity.getCustomerId())) {
                throw new BusinessException(ERROR_2,
                        "该手机账号已关联" + sysUserEntity.getCustomerName() + "，如需更换关联企业，在【账号管理】的详情页，更改客户即可。");
            } else {
                // 只需要做客户账号关联时需要更新创建人
                userMapper.updateUserCustomer(sysUserEntity.getUserId(), createDTO.getCustomerId(),
                        createDTO.getCustomerName(), createDTO.getCreateBy());
                return "账号关联成功";
            }
        } else {
            // 创建新用户
            SysUserEntity newUser = new SysUserEntity();
            newUser.setUserName(createDTO.getPhonenumber()); // 使用手机号作为用户名
            newUser.setNickName(createDTO.getNickName());
            newUser.setPhonenumber(createDTO.getPhonenumber());
            newUser.setCustomerId(createDTO.getCustomerId());
            newUser.setCustomerName(createDTO.getCustomerName());
            newUser.setCarrierStatus(createDTO.getCarrierStatus() == null ? "enabled" : createDTO.getCarrierStatus());
            newUser.setAuthStatus("inactive"); // 未激活状态
            newUser.setCreateBy(createDTO.getCreateBy());
            newUser.setPlatform(Platform.EXCEL_IMPORT.getCode());
            newUser.setDelFlag("0");
            newUser.setUserType("C");

            sysUserService.save(newUser);
            return "账号创建成功";
        }
    }

    public String updateAccount(AccountUpdateDTO updateDTO) {
        SysUser user = userMapper.selectUserById(updateDTO.getUserId());
        if (user == null) {
            return "用户不存在";
        }

        user.setNickName(updateDTO.getNickName());
        user.setPhonenumber(updateDTO.getPhonenumber());
        user.setCustomerId(updateDTO.getCustomerId());
        user.setCustomerName(updateDTO.getCustomerName());
        user.setCarrierStatus(updateDTO.getCarrierStatus());
        user.setUpdateBy(updateDTO.getUpdateBy());
        user.setUpdateTime(new Date());

        userMapper.updateUser(user);
        return "更新成功";
    }

    public List<AccountImportFailDTO> batchImportAccount(List<AccountCreateDTO> importList, String createBy) {
        List<AccountImportFailDTO> failList = new ArrayList<>();
        Pattern phonePattern = Pattern.compile("^1\\d{10}$");

        for (AccountCreateDTO dto : importList) {
            dto.setCreateBy(createBy);
            String failReason = validateImportData(dto, phonePattern);

            if (StringUtils.hasText(failReason)) {
                failList.add(AccountImportFailDTO.builder()
                        .nickName(dto.getNickName())
                        .phonenumber(dto.getPhonenumber())
                        .customerName(dto.getCustomerName())
                        .failReason(failReason)
                        .build());
            } else {
                try {
                    // 这里可以根据客户名称查询客户ID，暂时使用客户名称作为ID
                    if (StringUtils.hasText(dto.getCustomerName()) && !StringUtils.hasText(dto.getCustomerId())) {
                        Customer customer = iCustomerService.selectCustomerByName(dto.getCustomerName());
                        if (customer != null) {
                            dto.setCustomerId(customer.getCustomerId());
                        }
                    }
                    insertAccount(dto);
                } catch (BusinessException e) {
                    failList.add(AccountImportFailDTO.builder()
                            .nickName(dto.getNickName())
                            .phonenumber(dto.getPhonenumber())
                            .customerName(dto.getCustomerName())
                            .failReason("账号已关联其他企业")
                            .build());
                } catch (Exception e) {
                    failList.add(AccountImportFailDTO.builder()
                            .nickName(dto.getNickName())
                            .phonenumber(dto.getPhonenumber())
                            .customerName(dto.getCustomerName())
                            .failReason("账号已关联其他企业")
                            .build());
                }
            }
        }
        return failList;
    }

    private String validateImportData(AccountCreateDTO dto, Pattern phonePattern) {
        if (!StringUtils.hasText(dto.getNickName())) {
            return "昵称不能为空";
        }
        if (!StringUtils.hasText(dto.getPhonenumber()) || !phonePattern.matcher(dto.getPhonenumber()).matches()) {
            return "手机号格式错误";
        }
        if (!StringUtils.hasText(dto.getCustomerName())) {
            return "客户名称错误";
        }
        Customer customer = iCustomerService.selectCustomerByName(dto.getCustomerName());
        if (customer == null) {
            return "客户名称错误";
        }

        return null;
    }

    private AccountVO convertToAccountVO(SysUser user) {
        AccountVO accountVO = AccountVO.builder()
                .userId(user.getUserId())
                .createBy(user.getCreateBy())
                .phonenumber(user.getPhonenumber())
                .nickName(user.getNickName())
                .customerName(user.getCustomerName())
                .customerId(user.getCustomerId())
                .status(user.getStatus())
                .carrierStatus(user.getCarrierStatus())
                .build();

        // 格式化创建时间为标准字符串格式
        if (user.getCreateTime() != null) {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = sdf.format(user.getCreateTime());
            accountVO.setCreateTime(formattedDate);
        }

        return accountVO;
    }

}