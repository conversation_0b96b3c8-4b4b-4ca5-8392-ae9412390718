package com.yuanshuo.platform.admin.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanshuo.platform.admin.domain.entity.SysUserEntity;
import com.yuanshuo.platform.admin.domain.mapper.SysUserNewMapper;
import org.springframework.stereotype.Repository;

@Repository
public class SysUserNewRepository extends ServiceImpl<SysUserNewMapper, SysUserEntity>
        implements IService<SysUserEntity> {

    public SysUserEntity selectUserByPhoneAndUserType(String phoneNumber, String userType) {
        return this.getOne(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getPhonenumber, phoneNumber)
                .eq(SysUserEntity::getUserType, userType)
                .ne(SysUserEntity::getAuthStatus, "cancelled")
                .last("LIMIT 1"));
    }

    public SysUserEntity selectUserByUserNameAndUserType(String userName, String userType) {
        return this.getOne(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getUserName, userName)
                .eq(SysUserEntity::getUserType, userType)
                .ne(SysUserEntity::getAuthStatus, "cancelled")
                .last("LIMIT 1"));
    }
}
