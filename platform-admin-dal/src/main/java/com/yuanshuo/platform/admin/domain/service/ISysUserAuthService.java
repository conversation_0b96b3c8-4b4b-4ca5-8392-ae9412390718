package com.yuanshuo.platform.admin.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth;

import java.util.List;

/**
 * 用户登录方式管理业务层
 *
 * <AUTHOR>
 */
public interface ISysUserAuthService extends IService<SysUserAuth> {

    /**
     * 根据登录类型和登录标识获取用户登录方式
     * @param authType 登录类型
     * @param identifier 登录标识
     * @return
     */
    public SysUserAuth selectUserAuthByAuthTypeAndIdentifier(String authType, String identifier);

    /**
     * 新增
     */
    public int insertUserAuth(SysUserAuth userAuth);

    /**
     * 修改
     */
    public int updateUserAuth(SysUserAuth userAuth);

    /**
     * 删除
     */
    public int deleteUserAuthByUserId(Long userId);

    /**
     * 批量新增
     */
    public int batchInsertUserAuth(List<SysUserAuth> userAuths);

    /**
     * 根据userId查询登录方式列表
     */
    public List<SysUserAuth> selectUserAuthListByUserId(Long userId);

    /**
     * 根据getAuthType查询登录方式列表
     */
    public List<SysUserAuth> selectUserAuthListByAuthType(String authType);
}
