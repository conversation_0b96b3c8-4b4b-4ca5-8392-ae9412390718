package com.yuanshuo.platform.admin.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanshuo.platform.admin.common.utils.SecurityUtils;
import com.yuanshuo.platform.admin.domain.entity.Customer;
import com.yuanshuo.platform.admin.domain.mapper.CustomerMapper;
import com.yuanshuo.platform.admin.domain.service.ICustomerService;
import com.yuanshuo.platform.admin.domain.service.query.CustomerQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CustomerServiceImpl implements ICustomerService {

    @Resource
    private CustomerMapper customerMapper;

    @Override
    public int insert(Customer customer) {
        customer.setCreateUser(SecurityUtils.getUserId().toString());
        customer.setCreateName(SecurityUtils.getUsername());
        return customerMapper.insert(customer);
    }

    @Override
    public IPage<Customer> selectPage(Page<Customer> page, LambdaQueryWrapper<Customer> queryWrapper) {
        return customerMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Customer selectByCustomerId(String customerId) {

        // 2. 构建查询条件（所有条件为“且”关系）
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getCustomerId, customerId);
        queryWrapper.eq(Customer::getIsDelete, 0);
        return customerMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateById(Customer customer) {
        UpdateWrapper<Customer> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", customer.getId())
                .eq("is_delete", 0); // 确保只更新未删除的记录

        // 使用MyBatis-Plus的update方法，传入entity和wrapper
        customerMapper.update(customer, updateWrapper);
    }

    @Override
    public Customer selectCustomerByName(String customerFullName) {
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getCustomerFullName, customerFullName);
        queryWrapper.eq(Customer::getIsDelete, 0);
        return customerMapper.selectOne(queryWrapper);
    }

    @Override
    public List<Customer> queryCustomerList(CustomerQuery customerQuery) {
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        List<String> customerIds = customerQuery.getCustomerIds();
        if (!CollectionUtil.isEmpty(customerIds)) {
            queryWrapper.in(Customer::getCustomerId, customerIds);
        }
        if(StringUtils.isNotBlank(customerQuery.getCustomerFullName())){
            queryWrapper.like(Customer::getCustomerFullName, customerQuery.getCustomerFullName());
        }
        queryWrapper.eq(Customer::getIsDelete, 0);
        return customerMapper.selectList(queryWrapper);
    }
}
