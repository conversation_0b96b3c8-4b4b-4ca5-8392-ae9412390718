package com.yuanshuo.platform.admin.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth;
import com.yuanshuo.platform.admin.domain.mapper.SysUserAuthMapper;
import com.yuanshuo.platform.admin.domain.service.ISysUserAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户登录方式管理业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysUserAuthServiceImpl extends ServiceImpl<SysUserAuthMapper, SysUserAuth> implements ISysUserAuthService {

    @Autowired
    private SysUserAuthMapper userAuthMapper;

    @Override
    public SysUserAuth selectUserAuthByAuthTypeAndIdentifier(String authType, String identifier) {
        LambdaQueryWrapper<SysUserAuth> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysUserAuth::getAuthType, authType)
                .eq(SysUserAuth::getIdentifier, identifier)
                .eq(SysUserAuth::getIsDelete, 0);
        return this.getOne(queryWrapper);
    }

    @Override
    public int insertUserAuth(SysUserAuth userAuth) {
        return userAuthMapper.insertUserAuth(userAuth);
    }

    @Override
    public int updateUserAuth(SysUserAuth userAuth) {
        return userAuthMapper.updateUserAuth(userAuth);
    }

    @Override
    public int deleteUserAuthByUserId(Long userId) {
        return userAuthMapper.deleteUserAuthByUserId(userId);
    }

    @Override
    public int batchInsertUserAuth(List<SysUserAuth> userAuths) {
        return userAuthMapper.batchInsertUserAuth(userAuths);
    }

    @Override
    public List<SysUserAuth> selectUserAuthListByUserId(Long userId) {
        LambdaQueryWrapper<SysUserAuth> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysUserAuth::getUserId, userId)
                .eq(SysUserAuth::getIsDelete, 0);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysUserAuth> selectUserAuthListByAuthType(String authType) {
        return this.list(Wrappers.lambdaQuery(SysUserAuth.class).eq(SysUserAuth::getAuthType, authType));
    }
}
