<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.admin.domain.mapper.CustomerMapper">


    <resultMap id="BaseResultMap" type="com.yuanshuo.platform.admin.domain.entity.Customer">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="customer_id" property="customerId" jdbcType="VARCHAR"/>
        <result column="customer_full_name" property="customerFullName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="customer_type" property="customerType" jdbcType="TINYINT"/>
        <result column="industry" property="industry" jdbcType="TINYINT"/>
        <result column="unified_social_credit_code" property="unifiedSocialCreditCode" jdbcType="VARCHAR"/>
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="city_code" property="cityCode" jdbcType="VARCHAR"/>
        <result column="detail_address" property="detailAddress" jdbcType="VARCHAR"/>
        <result column="monthly_account" property="monthlyAccount" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="station_ids" property="stationIds" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, customer_id, customer_full_name, status, customer_type, industry,
        unified_social_credit_code, contact_phone, city_code, detail_address,
        monthly_account, create_user, create_time,
        update_time, is_delete, station_ids
    </sql>
    
</mapper>