<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.admin.domain.mapper.SysUserAuthMapper">

    <resultMap type="com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth"
               id="SysUserAuthResultMap">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="authType" column="auth_type"/>
        <result property="identifier" column="identifier"/>
        <result property="status" column="status"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="selectUserAuthVo">
        id,
        user_id,
        auth_type,
        identifier,
        status,
        is_delete,
        create_time,
        update_time
    </sql>

    <insert id="insertUserAuth" parameterType="com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth">
        insert into sys_user_auth
        (
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="auth_type != null and auth_type != ''">authType,</if>
        <if test="identifier != null and identifier != ''">identifier,</if>
        <if test="status != null">status,</if>
        create_time
        )
        values
        (
        <if test="userId != null and userId != 0">#{userId},</if>
        <if test="authType != null and authType != ''">#{authType},</if>
        <if test="identifier != null and identifier != ''">#{identifier},</if>
        <if test="status != null">#{status},</if>
        sysdate()
        )
    </insert>
    
    <update id="updateUserAuth" parameterType="com.yuanshuo.platform.admin.common.core.domain.entity.SysUserAuth">
        update sys_user_auth
        <set>        
            <if test="userId != null and userId != 0">user_id = #{userId},</if>
            <if test="authType != null and authType != ''">auth_type = #{authType},</if>
            <if test="identifier != null and identifier != ''">identifier = #{identifier},</if>
            <if test="status != null">status = #{status},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>
    
    <select id="selectUserAuthBy" resultMap="SysUserAuthResultMap" parameterType="int">
        select
        <include refid="selectUserAuthVo"/>
        from sys_user_auth
        where 1=1
        <if test="userId != null and userId != 0">
            and user_id = #{userId}
        </if>
        <if test="authType != null and authType != ''">
            and auth_type = #{authType}
        </if>
        <if test="identifier != null and identifier != ''">
            and identifier = #{identifier}
        </if>
    </select>

    <delete id="deleteUserAuthByUserId" parameterType="Long">
        delete from sys_user_auth where user_id = #{userId}
    </delete>


    <!-- 批量插入用户登录方式信息 -->
    <insert id="batchInsertUserAuth" parameterType="java.util.List">
        insert into sys_user_auth
        (
        user_id,
        auth_type,
        identifier,
        status,
        create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userId},
            #{item.authType},
            #{item.identifier},
            #{item.status},
            sysdate()
            )
        </foreach>
    </insert>



</mapper> 