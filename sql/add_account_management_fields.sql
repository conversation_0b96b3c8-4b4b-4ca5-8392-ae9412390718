-- 添加账号管理相关字段到sys_user表
ALTER TABLE sys_user ADD COLUMN customer_id VARCHAR(50) COMMENT '客户ID（预留字段）';
ALTER TABLE sys_user ADD COLUMN customer_name VARCHAR(100) COMMENT '客户名称（预留字段）';
ALTER TABLE sys_user ADD COLUMN carrier_status VARCHAR(20) DEFAULT 'disabled' COMMENT '承运开通状态（enabled/disabled）';

-- 添加索引以提高查询性能
CREATE INDEX idx_sys_user_customer_id ON sys_user(customer_id);
CREATE INDEX idx_sys_user_phonenumber ON sys_user(phonenumber);
CREATE INDEX idx_sys_user_nick_name ON sys_user(nick_name);
CREATE INDEX idx_sys_user_carrier_status ON sys_user(carrier_status);