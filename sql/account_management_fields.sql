-- 账号管理新增字段
ALTER TABLE sys_user ADD COLUMN customer_id VARCHAR(50) COMMENT '客户ID';
ALTER TABLE sys_user ADD COLUMN customer_name VARCHAR(100) COMMENT '客户名称';  
ALTER TABLE sys_user ADD COLUMN carrier_status VARCHAR(20) DEFAULT 'disabled' COMMENT '承运开通状态';

-- 索引
CREATE INDEX idx_customer_id ON sys_user(customer_id);
CREATE INDEX idx_phonenumber ON sys_user(phonenumber);
CREATE INDEX idx_nick_name ON sys_user(nick_name);