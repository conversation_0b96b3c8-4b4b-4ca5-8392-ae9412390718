package com.yuanshuo.platform.admin.api.model.vo;

import lombok.Data;

/**
 * 用户注销检查结果VO
 * 
 * <AUTHOR>
 */
@Data
public class UserCancelCheckVO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 是否可以注销
     */
    private Boolean canCancel;
    
    /**
     * 是否存在合同关系
     */
    private Boolean hasContract;
    
    /**
     * 合同关系描述
     */
    private String contractMessage;
    
    /**
     * 是否有未完成订单
     */
    private Boolean hasUnfinishedOrder;
    
    /**
     * 未完成订单描述
     */
    private String orderMessage;
    
    /**
     * 未完成订单数量
     */
    private Long unfinishedOrderCount;
    
    /**
     * 阻止注销的原因
     */
    private String blockReason;
    
    /**
     * 用户手机号
     */
    private String phoneNumber;
    
    /**
     * 客户名称（如果存在合同关系）
     */
    private String customerName;
}