package com.yuanshuo.platform.admin.api.feign;

import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.SysUserDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.SalespersonQueryDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.UserQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 用户管理
 *         <dependency>
 *             <groupId>com.yuanshuo</groupId>
 *             <artifactId>platform-admin-api</artifactId>
 *             <version>1.0.0-SNAPSHOT</version>
 *         </dependency>
 */
@FeignClient(value = "platform-admin",contextId = "adminUser")
public interface AdminUserFeign {
    
    @PostMapping("/admin/rpc/user/list")
    R<List<SysUserDTO>> list(@RequestBody UserQueryDTO request);


    @PostMapping("/admin/rpc/user/getSalesperson")
    R<List<SysUserDTO>> getSalesperson(@RequestBody SalespersonQueryDTO salespersonQueryDTO);
}