package com.yuanshuo.platform.admin.api.feign;


import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.WxSendMessageDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "platform-admin",contextId = "wxSendFeign")
public interface WxSendFeign {

    @PostMapping("/admin/rpc/wxSend/sendTextMessage")
    R<Void> sendTextMessage(@RequestBody WxSendMessageDTO wxSendMessageDTO);
}
