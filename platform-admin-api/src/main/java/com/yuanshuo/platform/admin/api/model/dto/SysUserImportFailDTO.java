package com.yuanshuo.platform.admin.api.model.dto;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户导入失败DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SysUserImportFailDTO {

    /**
     * 手机号
     */
    @Excel(name = "手机号", type = Excel.Type.EXPORT)
    private String phonenumber;

    /**
     * 是否为白名单
     */
    @Excel(name = "是否为白名单", type = Excel.Type.EXPORT)
    private String isWhitelistStr;

    /**
     * 失败原因
     */
    @Excel(name = "失败原因", type = Excel.Type.EXPORT)
    private String failReason;
}