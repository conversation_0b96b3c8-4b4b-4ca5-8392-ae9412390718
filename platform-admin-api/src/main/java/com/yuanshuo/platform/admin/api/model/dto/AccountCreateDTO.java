package com.yuanshuo.platform.admin.api.model.dto;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 账号创建DTO
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountCreateDTO {

    /**
     * 昵称
     */
    @Excel(name = "昵称")
    @NotBlank(message = "昵称不能为空")
    private String nickName;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1\\d{10}$", message = "手机号格式不正确")
    private String phonenumber;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    @Excel(name = "客户")
    private String customerName;

    /**
     * 承运开通状态（enabled/disabled）
     */
    private String carrierStatus;

    /**
     * 创建人
     */
    private String createBy;
}