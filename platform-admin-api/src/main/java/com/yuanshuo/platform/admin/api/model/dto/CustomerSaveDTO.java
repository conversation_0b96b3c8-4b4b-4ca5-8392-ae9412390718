package com.yuanshuo.platform.admin.api.model.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

@Data
public class CustomerSaveDTO {

    /**
     * 客户全称（1-20字，中英文，必填）
     */
    @NotBlank(message = "客户全称不能为空")
    @Size(min = 1, max = 40, message = "客户全称长度必须在1-40个字符之间")
    private String customerFullName;

    /**
     * 状态：1-启用，0-禁用
     */
    @NotNull(message = "请选择客户状态")
    @Min(value = 0, message = "状态值无效")
    @Max(value = 1, message = "状态值无效")
    private Integer status;

    /**
     * 客户性质：1-企业，0-个人
     */
    @NotNull(message = "请选择客户性质")
    @Min(value = 0, message = "客户性质值无效")
    @Max(value = 1, message = "客户性质值无效")
    private Integer customerType;

    /**
     * 行业：1-快递，2-生鲜，3-医药，4-商超零售，5-布草，6-餐具，7-汽配，8-其他
     */
    @NotNull(message = "请选择行业")
    private Integer industry;

    /**
     * 社会统一信用代码（企业性质时必填）
     */
    private String unifiedSocialCreditCode;

    /**
     * 联系方式（手机号，必填）
     */
    @NotBlank(message = "请输入手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String contactPhone;

    /**
     * 城市（城市-省市级联）
     */
    @NotBlank(message = "请选择城市")
    private String cityCode;

    /**
     * 详细地址
     */
    @NotBlank(message = "请输入详细地址")
    private String detailAddress;

    /**
     * 分配点位
     */
    @NotEmpty(message = "请分配点位")
    private List<String> stationIdList;;
}
