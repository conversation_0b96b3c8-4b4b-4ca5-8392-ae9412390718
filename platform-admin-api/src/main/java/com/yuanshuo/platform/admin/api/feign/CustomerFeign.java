package com.yuanshuo.platform.admin.api.feign;


import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.admin.api.model.dto.CustomerQueryListDTO;
import com.yuanshuo.platform.admin.api.model.vo.CustomerVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "platform-admin",contextId = "customerFeign")
public interface CustomerFeign {

    @PostMapping("/admin/rpc/customer/list")
    R<List<CustomerVO>> list(@RequestBody CustomerQueryListDTO customerQueryListDTO);

}
