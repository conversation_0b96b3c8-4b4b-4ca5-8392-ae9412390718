package com.yuanshuo.platform.admin.api.model.vo;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;



/**
 * 系统用户查询响应VO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SysUserQueryVO {

    /**
     * 用户ID
     */
    @Excel(name = "用户ID", type = Excel.Type.EXPORT)
    private Long userId;

    /**
     * 注册来源
     */
    @Excel(name = "注册来源", type = Excel.Type.EXPORT)
    private String platform;

    /**
     * 手机号
     */
    @Excel(name = "手机号", type = Excel.Type.EXPORT)
    private String phonenumber;

    /**
     * 昵称
     */
    @Excel(name = "昵称", type = Excel.Type.EXPORT)
    private String nickName;

    /**
     * 注册时间
     */
    @Excel(name = "注册时间", type = Excel.Type.EXPORT)
    private String createTime;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 是否为企业用户
     */
    @Excel(name = "是否为企业用户", readConverterExp = "true=是,false=否", type = Excel.Type.EXPORT)
    private Boolean isCorporate;

    /**
     * 企业
     */
    @Excel(name = "企业名称", type = Excel.Type.EXPORT)
    private String customerName;

    /**
     * 企业账号状态
     */
    @Excel(name = "企业账号状态", type = Excel.Type.EXPORT)
    private String carrierStatus;

    /**
     * 历史订单数量
     */
    @Excel(name = "历史订单数量", type = Excel.Type.EXPORT)
    private Long orderCount;

    /**
     * 优惠券数量
     */
    @Excel(name = "优惠券数量", type = Excel.Type.EXPORT)
    private Long couponCount;

    /**
     * 是否为白名单用户
     */
    @Excel(name = "是否为白名单", readConverterExp = "true=是,false=否", type = Excel.Type.EXPORT)
    private Boolean isWhitelist;

    /**
     * 邀请人用户ID
     */
    @Excel(name = "邀请人ID", type = Excel.Type.EXPORT)
    private Long inviterUserId;

    /**
     * 邀请人姓名
     */
    @Excel(name = "邀请人", type = Excel.Type.EXPORT)
    private String inviterName;

    /**
     * 用户认证状态
     */
    @Excel(name = "用户状态", readConverterExp = "inactive=未激活,unverified=未实名,verified=已实名,cancelled=已注销", type = Excel.Type.EXPORT)
    private String authStatus;
}