package com.yuanshuo.platform.admin.api.model.dto;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 用户导入DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SysUserImportDTO {

    /**
     * 手机号
     */
    @Excel(name = "手机号", type = Excel.Type.IMPORT)
    @NotBlank(message = "手机号不能为空")
    private String phonenumber;

    /**
     * 是否为白名单
     */
    @Excel(name = "是否白名单", type = Excel.Type.IMPORT)
    private String isWhitelistStr;

    /**
     * 是否为白名单（转换后的布尔值）
     */
    private Boolean isWhitelist;
}