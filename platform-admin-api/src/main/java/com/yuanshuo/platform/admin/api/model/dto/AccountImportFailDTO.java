package com.yuanshuo.platform.admin.api.model.dto;

import com.yuanshuo.platform.admin.api.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账号导入失败记录DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountImportFailDTO {

    @Excel(name = "昵称")
    private String nickName;

    @Excel(name = "手机号")
    private String phonenumber;

    @Excel(name = "客户")
    private String customerName;

    @Excel(name = "失败原因")
    private String failReason;
}