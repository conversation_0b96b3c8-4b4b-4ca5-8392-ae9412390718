package com.yuanshuo.platform.admin.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 联系人DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContactDTO {

    /**
     * 联系人ID（修改时必填）
     */
    private Long id;

    /**
     * 联系人姓名
     */

    @Size(max = 10, message = "姓名长度不能超过10个字符")
    @Pattern(regexp = "^[a-zA-Z0-9\u4e00-\u9fa5_-]*$", message = "姓名只能包含字母、数字、中文、下划线和横线")
    private String name;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    @Pattern(regexp = "^1\\d{10}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 关联的用户ID
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;
}