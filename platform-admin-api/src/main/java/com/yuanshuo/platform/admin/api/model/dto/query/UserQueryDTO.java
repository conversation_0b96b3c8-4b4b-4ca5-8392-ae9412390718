package com.yuanshuo.platform.admin.api.model.dto.query;

import com.yuanshuo.platform.admin.api.enums.AuthType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户查询DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserQueryDTO {

    /**
     * 用户ID列表
     */
    private List<Long> ids;

    /**
     * 用户类型
     * @see AuthType#getCode()
     *
     */
    private String userType;
}