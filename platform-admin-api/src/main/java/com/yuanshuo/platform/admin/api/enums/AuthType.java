package com.yuanshuo.platform.admin.api.enums;

import lombok.Getter;

@Getter
public enum AuthType {

    // Phone/Email/WxMpMp/AliMp/WeCom/Password
    Phone("Phone", "手机号"),
    Email("Email", "邮箱"),
    WxMp("WxMp", "微信小程序"),
    AliMp("AliMp", "支付宝小程序"),
    WeCom("WeCom", "企业微信"),
    Password("Password", "密码");

    private final String code;
    private final String desc;

    AuthType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
