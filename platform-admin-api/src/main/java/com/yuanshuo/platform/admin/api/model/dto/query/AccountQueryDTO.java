package com.yuanshuo.platform.admin.api.model.dto.query;

import com.yuanshuo.common.entity.support.PageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 账号查询DTO
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountQueryDTO extends PageQuery {

    /**
     * 手机号（模糊查询）
     */
    private String phonenumber;

    /**
     * 昵称（模糊查询）
     */
    private String nickName;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 账号状态,OK("0", "正常"), DISABLE("1", "停用"), DELETED("2", "删除");
     */
    private String status;

    /**
     * 承运开通状态
     */
    private String carrierStatus;
}