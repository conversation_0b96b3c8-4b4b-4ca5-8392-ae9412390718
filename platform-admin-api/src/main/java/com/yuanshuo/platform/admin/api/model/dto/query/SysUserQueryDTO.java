package com.yuanshuo.platform.admin.api.model.dto.query;

import com.yuanshuo.common.entity.support.PageQuery;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 系统用户查询DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString
public class SysUserQueryDTO extends PageQuery {

    /**
     * 用户状态 OK("0", "正常"), DISABLE("1", "停用"), DELETED("2", "删除");
     */
    private String status;

    /**
     * 注册时间开始
     */
    private Date createTimeStart;

    /**
     * 注册时间结束
     */
    private Date createTimeEnd;

    /**
     * 注册来源
     * @see Platform#getCode()
     */
    private String platform;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;

    /**
     * 用户ID模糊查询
     */
    private String userId;

    /**
     * 手机号
     */
    private String phonenumber;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 是否为企业用户
     */
    private Boolean  isCorporate;

    /**
     * 是否为白名单用户
     */
    private Boolean isWhitelist;

    private String authStatus;

}