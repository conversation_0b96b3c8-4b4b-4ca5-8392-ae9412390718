package com.yuanshuo.platform.admin.api.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CustomerVO {

    /**
     * 自增ID（隐藏字段）
     */
    private Long id;
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createName;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 客户全称
     */
    private String customerFullName;
    /**
     * 客户性质（文本描述）
     */
    private String customerTypeDesc;
    /**
     * 行业（文本描述）
     */
    private String industryDesc;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     *  城市
     */
    private String cityName;
    /**
     * status
     */
    private Integer status;
    /**
     * 状态（文本描述）
     */
    private String statusDesc;

    /**
     * 社会统一信用代码
     */
    private String unifiedSocialCreditCode;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 月结账号
     */
    private String monthlyAccount;
    /**
     * 月结账号状态（文本描述）
     */
    private String monthlyAccountStatusDesc;

    /**
     * 分配点位列表
     */
    private List<String> stationList;

    /**
     * 是否绑定有效的合同
     */
    private Boolean validContractBound = false;
}
