package com.yuanshuo.platform.admin.api.enums;

import lombok.Getter;

/**
 * 平台枚举
 *
 * <AUTHOR>
 */
@Getter
public enum Platform {

    SYSTEM("system", "平台注册"),

    APP("app","小元智送APP"),
    XYZS_MINI_PROGRAM("xyzs_mini_program","小元智送小程序"),
    XYCK_MINI_PROGRAM("xyck_mini_program","小元车控小程序"),
    OTHER("other","其他"),

    EXCEL_IMPORT("excel_import", "平台导入");


    private String code;
    private String desc;

    Platform(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //根据code获取枚举
    public static Platform getEnumByCode(String code) {
        for (Platform platform : Platform.values()) {
            if (platform.getCode().equals(code)) {
                return platform;
            }
        }
        return OTHER;
    }

}
