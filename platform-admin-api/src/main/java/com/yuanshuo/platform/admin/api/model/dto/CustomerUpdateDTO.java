package com.yuanshuo.platform.admin.api.model.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

@Data
public class CustomerUpdateDTO {

    /**
     * 客户ID（用于定位待更新的客户，不可编辑）
     */
    @NotBlank(message = "客户ID不能为空")
    private String customerId;

    /**
     * 客户状态（1-启用，0-禁用，可编辑）
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 客户全称（1-20字，中英文，必填，平台唯一）
     */
    @NotBlank(message = "客户全称不能为空")
    @Size(min = 1, max = 40, message = "客户全称长度必须为1-40字")
    private String customerFullName;

    /**
     * 客户性质（1-企业，0-个人，必填，可编辑）
     */
    @NotNull(message = "客户性质不能为空")
    private Integer customerType;

    /**
     * 行业类型（1-快递/2-生鲜等，必填，可编辑）
     */
    @NotNull(message = "行业不能为空")
    private Integer industry;

    /**
     * 社会统一信用代码（18位数字和大写字母，企业性质时必填）
     */
    private String unifiedSocialCreditCode;

    /**
     * 联系方式（手机号，必填，校验格式）
     */
    @NotBlank(message = "联系方式不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String contactPhone;

    /**
     * 城市编码（省市级联，必填，可编辑）
     */
    @NotBlank(message = "城市不能为空")
    private String cityCode;

    /**
     * 详细地址（必填，可编辑，对接高德API）
     */
    @NotBlank(message = "详细地址不能为空")
    private String detailAddress;

    /**
     * 分配的点位ID列表（至少1个，按选择顺序排序）
     */
    @NotEmpty(message = "至少选择1个点位")
    private List<String> stationIdList;
}
