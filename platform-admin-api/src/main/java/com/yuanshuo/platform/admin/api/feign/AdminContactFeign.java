package com.yuanshuo.platform.admin.api.feign;

import com.yuanshuo.platform.admin.api.model.dto.ContactDTO;
import com.yuanshuo.platform.admin.api.model.dto.query.ContactQueryDTO;
import com.yuanshuo.platform.admin.api.model.vo.ContactVO;
import lombok.extern.java.Log;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.yuanshuo.common.entity.web.R;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import java.util.List;

/**
 * 常用联系人管理
 *         <dependency>
 *             <groupId>com.yuanshuo</groupId>
 *             <artifactId>platform-admin-api</artifactId>
 *             <version>1.0.0-SNAPSHOT</version>
 *         </dependency>
 */
@FeignClient(value = "platform-admin",contextId = "adminContact")
public interface AdminContactFeign {
    @PostMapping("/admin/rpc/contact/getRecommendationList")
    R<List<ContactVO>> getRecommendationList(@RequestBody ContactQueryDTO queryDTO);

    @PostMapping("/admin/rpc/contact/list")
    R<List<ContactVO>> list(@RequestBody ContactQueryDTO queryDTO);

    @PostMapping(value = "/admin/rpc/contact/getById")
    R<ContactVO> getById(@RequestBody Long id);

    @PostMapping("/admin/rpc/contact/add")
    R<Long> add(@Valid @RequestBody ContactDTO contactDTO);

    @PostMapping("/admin/rpc/contact/edit")
    R edit(@Valid @RequestBody ContactDTO contactDTO);

    @PostMapping("/admin/rpc/contact/delete")
    R remove(@RequestBody Long id);
}
