package com.yuanshuo.platform.admin.api.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CustomerDetailVO {

    /**
     * 客户ID（系统生成，不可编辑）
     */
    private String customerId;

    /**
     * 创建人（系统生成，不可编辑）
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间（系统生成，不可编辑）
     */
    private String createTime;

    /**
     * 状态（1-启用，0-禁用，可编辑）
     */
    private Integer status;

    /**
     * 客户全称（1-20字，中英文，必填，平台唯一）
     */
    private String customerFullName;

    /**
     * 客户性质（1-企业，0-个人，可编辑）
     */
    private Integer customerType;

    /**
     * 行业（1-快递/2-生鲜/.../8-其他，可编辑）
     */
    private Integer industry;

    /**
     * 社会统一信用代码（18位数字和大写字母，企业必填）
     */
    private String unifiedSocialCreditCode;

    /**
     * 联系方式（手机号，校验格式，必填）
     */
    private String contactPhone;


    /**
     * 城市编码（省市级联，可编辑）
     */
    private String cityCode;

    /**
     * 详细地址（对接高德API，可编辑，必填）
     */
    private String detailAddress;

    /**
     * 月结账号（系统生成，不可编辑，平台唯一）
     */
    private String monthlyAccount;

    /**
     * true:生效中
     * false:已失效
     */
    private Boolean monthlyAccountStatus;

    /**
     * 月结账号状态描述（生效中/已失效，系统计算）
     * true:生效中
     * false:已失效
     */
    private String monthlyAccountStatusDesc;

    /**
     * 分配点位列表
     */
    private List<String> stationList;

    /**
     * 点位详情数组
     */
    private List<StationVO> stationDetailList;

    @Data
    public static class StationVO {
        /**
         * 点位id
         */
        private Long id;

        /**
         * 点位名称
         */
        private String locationName;

        /**
         * 点位地址
         */
        private String address;

        /**
         * 点位类型
         */
        private Integer locationType;

        /**
         * 点位类型中文
         */
        private String locationTypeName;

        /**
         * 状态
         */
        private String status;

        /**
         * 状态中文
         */
        private String statusName;
    }

}
