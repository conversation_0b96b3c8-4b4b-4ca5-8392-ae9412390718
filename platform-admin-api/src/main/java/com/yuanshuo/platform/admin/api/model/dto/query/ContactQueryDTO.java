package com.yuanshuo.platform.admin.api.model.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 联系人查询DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContactQueryDTO {

    /**
     * 查询关键字（支持姓名和电话模糊查询）
     */
    private String keyword;
    
    /**
     * 关联的用户ID
     */
    private Long userId;

    /**
     * 点位code
     */
    private String locationId;

}