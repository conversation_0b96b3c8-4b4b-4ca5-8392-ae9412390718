package com.yuanshuo.platform.admin.api.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 账号VO
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 手机号
     */
    private String phonenumber;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 关联客户
     */
    private String customerName;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 承运开通状态
     */
    private String carrierStatus;
}